import { Template, WaTemplateCta, SendTemplateRequest, WhatsAppTemplate, ButtonComponent } from '@/types/whatsapp.js';
import { SendTemplateRequestSchema, ValidatedTemplateRequest } from '@/validations/templateValidation.js';

// Add interface for WhatsApp template input
interface WhatsAppTemplateInput {
    name: string;
    parameter_format?: string;
    components: Array<{
        type: 'HEADER' | 'BODY' | 'FOOTER' | 'BUTTONS';
        format?: 'TEXT' | 'IMAGE' | 'VIDEO' | 'DOCUMENT';
        text?: string;
        example?: {
            header_handle?: string[];
        };
        buttons?: Array<{
            type: 'URL' | 'PHONE_NUMBER' | 'QUICK_REPLY';
            text: string;
            url?: string;
            phone_number?: string;
            example?: string[];
        }>;
    }>;
    language?: string;
    status?: string;
    category?: string;
    sub_category?: string;
    id?: string;
}

// Template CTAs configuration
export const waTemplateCtas: { [key: string]: WaTemplateCta } = {
    "pay_dues": {
        id: 1,
        label: "Pay Now",
        value: "pay_dues",
        type: "quick_reply",
    },
    "order_history": {
        id: 2,
        label: "Request Details",
        value: "order_history",
        type: "quick_reply",
    },
    "help": {
        id: 3,
        label: "Contact Us",
        value: "help",
        type: "quick_reply",
    },
    "place_update_order": {
        id: 4,
        label: "Start Shopping",
        value: "place_update_order",
        type: "quick_reply",
    },
    "url_suffix": {
        id: 5,
        label: "url_suffix",
        value: "url_suffix",
        type: "url",
    },
    "clear_dues": {
        id: 6,
        label: "Clear Dues",
        value: "clear_dues",
        type: "quick_reply",
    },
    "claim_discount": {
        id: 7,
        label: "Claim Discount Now",
        value: "claim_discount",
        type: "quick_reply",
    },
    "visit_website": {
        id: 8,
        label: "Visit website",
        value: "visit_website",
        type: "url",
    },
    "hello": {
        id: 9,
        label: "Hello",
        value: "hello",
        type: "quick_reply",
    },
};

// Available templates configuration
export const templates: Template[] = [
    {
        id: 1,
        templateId: "item_discount_trial_offer",
        name: "Discount Offer",
        header: "",
        content: `*🎉 Exclusive Offer Just for You!* Grab a limited-time deal on *{{item_Name}}* now! 🎁 Save more while enjoying the best quality products. 🛒 Don't miss out—this offer ends soon!`,
        preview: `*🎉 Exclusive Offer Just for You!* \n\n Grab a limited-time deal on *{{item_Name}}* now! \n🎁 Save more while enjoying the best quality products. \n🛒 Don't miss out—this offer ends soon!`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.place_update_order, waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: ["item_name"],
        headerVariables: ["image_url"],
        lastUsed: "",
    },
    {
        id: 2,
        templateId: "early_pending_payment_special_offer",
        name: "Pending payment offer",
        header: "Settle balance and save!",
        content: `*🎉 Hi {{customer_name}},* \n\nYour account shows an outstanding balance of *₹{{pending_amount}}*. \nWe're offering an *exclusive discount* or priority services for settling this balance by *{{due_date}}* ! \n\n This balance reflects all partial payments made and outstanding amounts. Act now to unlock the benefits!`,
        preview: `*🎉 Hi {{customer_name}},* \n\nYour account shows an outstanding balance of *₹{{pending_amount}}*. \nWe're offering an *exclusive discount* or priority services for settling this balance by *{{due_date}}* ! \n\n This balance reflects all partial payments made and outstanding amounts. Act now to unlock the benefits!`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.pay_dues, waTemplateCtas.order_history],
        phone: "",
        bodyVariables: ["customer_name", "pending_amount", "due_date"],
        lastUsed: "",
    },
    {
        id: 3,
        templateId: "pending_payment_reminder",
        name: "Pending payment reminder",
        header: "Outstanding balance: ₹{{pending_amount}}",
        content: `👋 Dear {{customer_name}}, \n\nWe'd like to remind you that your current outstanding balance with us is ₹{{pending_amount}} as of {{today_date}}.\n\nThis may include multiple orders, and we appreciate your partial payments made. To ensure your account remains in good standing, we kindly request you to settle the remaining amount.\n\nTap below to clear your balance or reach out for a detailed breakdown.`,
        preview: `👋 Dear {{customer_name}}, \n\nWe'd like to remind you that your current outstanding balance with us is ₹{{pending_amount}} as of {{today_date}}.\n\nThis may include multiple orders, and we appreciate your partial payments made. To ensure your account remains in good standing, we kindly request you to settle the remaining amount.\n\nTap below to clear your balance or reach out for a detailed breakdown.`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.pay_dues, waTemplateCtas.order_history],
        phone: "",
        bodyVariables: ["customer_name", "pending_amount", "today_date"],
        headerVariables: ["pending_amount"],
        lastUsed: "",
    },
    {
        id: 4,
        templateId: "open_for_ordering",
        name: "Open for ordering",
        header: "Fresh, Fast, and Affordable",
        content: `*🍎 Hello,*\n\nWe're open for orders! Explore our wide range of products: \n\n🍅 Fresh Fruits & Vegetables\n🥖 Daily Groceries & Staples\n🧴 Personal Care Products\n🧃 Beverages and more!\n\nShop today for premium quality and hassle-free delivery.`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.place_update_order, waTemplateCtas.help],
        phone: "",
        bodyVariables: [],
        lastUsed: "",
    },
    {
        id: 5,
        templateId: "f_business_order_confirm",
        name: "Business Order Confirmation",
        header: "",
        content: "Hello {{1}} 👋,\nThank you for your order! 🛒 We've received your request for the following items:\n\n{{2}}\n\nYour order *#{{3}}* will be delivered tomorrow morning. 🚚\nTotal payable: ₹{{4}}. 💰\n\nThanks for choosing farmersMandi ! 🙏\n\nBest,\nfarmersMandi",
        preview: "Hello {{1}} 👋,\nThank you for your order! 🛒 We've received your request for the following items:\n\n{{2}}\n\nYour order *#{{3}}* will be delivered tomorrow morning. 🚚\nTotal payable: ₹{{4}}. 💰\n\nThanks for choosing farmersMandi ! 🙏\n\nBest,\nfarmersMandi",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1", "2", "3", "4"],
        lastUsed: "",
    },
    {
        id: 6,
        templateId: "mnet_seller_order_placed",
        name: "Seller Order Placed",
        header: "New order recieved",
        content: `:new: New Order Alert! \n Hi {{4}}, \nYou have a new order from {{3}}! \n :shopping_trolley: Order ID: {{1}} \n :moneybag: Amount: ₹{{2}} \n Please start processing the order promptly! :rocket:`,
        preview: `:new: New Order Alert! \n Hi {{4}}, \nYou have a new order from {{3}}! \n :shopping_trolley: Order ID: {{1}} \n :moneybag: Amount: ₹{{2}} \n Please start processing the order promptly! :rocket:`,
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1", "2", "3", "4"],
        lastUsed: "",
    },
    {
        id: 7,
        templateId: "pending_dues_buyer_1",
        name: "Pending Dues Reminder",
        header: "",
        content: "Hi {{1}},\nYou have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.\nThank you !",
        preview: "Hi {{1}},\nYou have pending dues with us. Please open the app to review and clear them as soon as possible. Ignore if already paid.\nThank you !",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1"],
        lastUsed: "",
    },
    {
        id: 8,
        templateId: "hello_mnet",
        name: "Hello Introduction",
        header: "",
        content: "Hello, thanks for requesting an introductory call from us. We will be in touch soon.",
        preview: "Hello, thanks for requesting an introductory call from us. We will be in touch soon.",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: [],
        lastUsed: "",
    },
    {
        id: 9,
        templateId: "hello_world",
        name: "Hello World",
        header: "Hello World",
        content: "Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.",
        preview: "Welcome and congratulations!! This message demonstrates your ability to send a WhatsApp message notification from the Cloud API, hosted by Meta. Thank you for taking the time to test with us.",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: [],
        lastUsed: "",
    },
    {
        id: 10,
        templateId: "delivery_completed",
        name: "Delivery Completed",
        header: "Delivery Completed",
        content: "Hi {{1}} 👋,\nYour order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗\n\nTotal amount paid: *₹{{3}}*. 💰\nThanks for choosing us! 🙏\n\nBest,\nfarmersMandi",
        preview: "Hi {{1}} 👋,\nYour order *#{{2}}* has been delivered. ✅ We hope you're happy with the fresh produce! 🥗\n\nTotal amount paid: *₹{{3}}*. 💰\nThanks for choosing us! 🙏\n\nBest,\nfarmersMandi",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1", "2", "3"],
        lastUsed: "",
    },
    {
        id: 11,
        templateId: "delivery_confirmation_with_credit",
        name: "Delivery with Credit",
        header: "Delivery Confirmation",
        content: "Hi {{1}} 👋,\nYour order #{{2}} has been delivered. ✅\nOrder Amount : ₹{{3}}\n\nTotal Amount due: *₹{{4}}*. 💰\nPlease pay soon. 💳\n\nBest,\nfarmersMandi",
        preview: "Hi {{1}} 👋,\nYour order #{{2}} has been delivered. ✅\nOrder Amount : ₹{{3}}\n\nTotal Amount due: *₹{{4}}*. 💰\nPlease pay soon. 💳\n\nBest,\nfarmersMandi",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1", "2", "3", "4"],
        lastUsed: "",
    },
    {
        id: 12,
        templateId: "complete_order_cancellation",
        name: "Order Cancellation",
        header: "Order Cancellation",
        content: "Dear {{1}},\n\nWe're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌\n\nWe apologize for the inconvenience and hope to serve you again. 🙏\n\nBest,\nfarmersMandi",
        preview: "Dear {{1}},\n\nWe're sorry, but your order #{{2}} couldn't be fulfilled due to stock unavailability.❌\n\nWe apologize for the inconvenience and hope to serve you again. 🙏\n\nBest,\nfarmersMandi",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1", "2"],
        lastUsed: "",
    },
    {
        id: 13,
        templateId: "out_for_delivery",
        name: "Out for Delivery",
        header: "Out for Delivery",
        content: "Good morning {{1}}! 🌅\nYour order #{{2}} is on the way and will be delivered soon. 🚚\n\nTotal payable on delivery: *₹{{3}}*\n\nThanks for choosing us! 🙏\n\nBest,\nfarmersMandi",
        preview: "Good morning {{1}}! 🌅\nYour order #{{2}} is on the way and will be delivered soon. 🚚\n\nTotal payable on delivery: *₹{{3}}*\n\nThanks for choosing us! 🙏\n\nBest,\nfarmersMandi",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["1", "2", "3"],
        lastUsed: "",
    },
    // B2B Templates
    {
        id: 14,
        templateId: "b2b_order_placed_confirmed_quick_v2",
        name: "B2B Order Confirmed (Quick)",
        header: "Great news we - just got your order!",
        content: "Hi *{{customer_name}}*,\n\nThanks a lot for your order! Your order **#{{order_id}}** is confirmed and will be on its way by *{{expt_delivery_time}}* today.\n\n*Order Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe really appreciate you working with us.\n\nCheers,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nThanks a lot for your order! Your order **#{{order_id}}** is confirmed and will be on its way by *{{expt_delivery_time}}* today.\n\n*Order Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe really appreciate you working with us.\n\nCheers,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "expt_delivery_time", "order_details", "total_amount"],
        lastUsed: "",
    },
    {
        id: 15,
        templateId: "b2b_order_out_for_delivery_v2",
        name: "B2B Order Out for Delivery",
        header: "Your order is on its way!",
        content: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** is out for delivery and should reach you by *{{delivery_time}}*.\n\nThanks for partnering with us.\n\nRegards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** is out for delivery and should reach you by *{{delivery_time}}*.\n\nThanks for partnering with us.\n\nRegards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "delivery_time"],
        lastUsed: "",
    },
    {
        id: 16,
        templateId: "b2b_order_delivered_v2",
        name: "B2B Order Delivered",
        header: "Delivery complete!",
        content: "Hi *{{customer_name}}*,\n\nWe're happy to let you know that your order **#{{order_id}}** has been delivered successfully. We hope everything meets your expectations!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for doing business with us.\n\nWarm regards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe're happy to let you know that your order **#{{order_id}}** has been delivered successfully. We hope everything meets your expectations!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for doing business with us.\n\nWarm regards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount"],
        lastUsed: "",
    },
    {
        id: 17,
        templateId: "b2b_order_delivered_credit_v2",
        name: "B2B Order Delivered (Credit)",
        header: "Order delivered – Payment on Credit",
        content: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nThank you for your continued partnership.\n\nBest regards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nThank you for your continued partnership.\n\nBest regards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "order_amount", "total_pending_amount"],
        lastUsed: "",
    },
    {
        id: 18,
        templateId: "b2b_order_cancelled_v2",
        name: "B2B Order Cancelled",
        header: "Order Cancellation Notice",
        content: "Hi *{{customer_name}}*,\n\nWe're sorry to inform you that your order **#{{order_id}}** has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nWe apologize for any inconvenience and thank you for your understanding.\n\nBest regards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe're sorry to inform you that your order **#{{order_id}}** has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nWe apologize for any inconvenience and thank you for your understanding.\n\nBest regards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "cancellation_reason", "refund_amount"],
        lastUsed: "",
    },
    {
        id: 19,
        templateId: "b2b_payment_success_v2",
        name: "B2B Payment Success",
        header: "Payment received – Thank you!",
        content: "Hi *{{customer_name}}*,\n\nWe've received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**. Everything is set on our end!\n\nThanks for your prompt action.\n\nCheers,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe've received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**. Everything is set on our end!\n\nThanks for your prompt action.\n\nCheers,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount"],
        lastUsed: "",
    },
    {
        id: 20,
        templateId: "b2b_payment_failed_v2",
        name: "B2B Payment Failed",
        header: "Payment issue – Lets sort it out",
        content: "Hi *{{customer_name}}*,\n\nWe couldn't process the payment for order **#{{order_id}}** (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThank you for your patience,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe couldn't process the payment for order **#{{order_id}}** (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThank you for your patience,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "attempted_amount"],
        lastUsed: "",
    },
    {
        id: 21,
        templateId: "b2b_prepaid_unconfirmed_v2",
        name: "B2B Prepaid Unconfirmed",
        header: "Order not confirmed – Cancelled",
        content: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**, but we couldn't confirm the order due to *{{reason}}*. As a result, the order has been cancelled and a refund of *₹{{refund_amount}}* has been processed.\n\nThanks for your patience,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**, but we couldn't confirm the order due to *{{reason}}*. As a result, the order has been cancelled and a refund of *₹{{refund_amount}}* has been processed.\n\nThanks for your patience,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount", "reason", "refund_amount"],
        lastUsed: "",
    },
    // B2C Templates
    {
        id: 22,
        templateId: "b2c_grocery_order_placed_confirmed_v2",
        name: "B2C Grocery Order Confirmed",
        header: "Order confirmed – Thank you!",
        content: "Hi *{{customer_name}}*,\n\nThanks so much for your order! Your order **#{{order_id}}** is confirmed and will reach you by *{{expt_delivery_time}}*.\n\n*Your Items:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe're excited to serve you and hope you enjoy your shopping experience with us.\n\nTake care,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nThanks so much for your order! Your order **#{{order_id}}** is confirmed and will reach you by *{{expt_delivery_time}}*.\n\n*Your Items:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe're excited to serve you and hope you enjoy your shopping experience with us.\n\nTake care,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "expt_delivery_time", "order_details", "total_amount"],
        lastUsed: "",
    },
    {
        id: 23,
        templateId: "b2c_grocery_order_out_for_delivery_v2",
        name: "B2C Grocery Out for Delivery",
        header: "Your order is on the way!",
        content: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** is out for delivery and should reach you by *{{delivery_time}}*. We're looking forward to delighting you with our service!\n\nBest,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** is out for delivery and should reach you by *{{delivery_time}}*. We're looking forward to delighting you with our service!\n\nBest,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "delivery_time"],
        lastUsed: "",
    },
    {
        id: 24,
        templateId: "b2c_grocery_order_delivered_v2",
        name: "B2C Grocery Delivered",
        header: "Delivery complete – Enjoy your day!",
        content: "Hi *{{customer_name}}*,\n\nGreat news – your order **#{{order_id}}** has been delivered. We hope everything is perfect!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for choosing us. We value your trust!\n\nWarm wishes,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nGreat news – your order **#{{order_id}}** has been delivered. We hope everything is perfect!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for choosing us. We value your trust!\n\nWarm wishes,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount"],
        lastUsed: "",
    },
    // Additional B2C Grocery Templates
    {
        id: 25,
        templateId: "b2c_grocery_order_delivered_credit_v2",
        name: "B2C Grocery Delivered (Credit)",
        header: "Order delivered – Payment on Credit",
        content: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nThank you for shopping with us.\n\nWarm regards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nThank you for shopping with us.\n\nWarm regards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "order_amount", "total_pending_amount"],
        lastUsed: "",
    },
    {
        id: 26,
        templateId: "b2c_grocery_order_cancelled_v2",
        name: "B2C Grocery Order Cancelled",
        header: "Order cancelled",
        content: "Hi *{{customer_name}}*,\n\nWe're sorry, but your order **#{{order_id}}** has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nThank you for understanding,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe're sorry, but your order **#{{order_id}}** has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nThank you for understanding,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "cancellation_reason", "refund_amount"],
        lastUsed: "",
    },
    {
        id: 27,
        templateId: "b2c_grocery_payment_success_v2",
        name: "B2C Grocery Payment Success",
        header: "Payment received – Thank you!",
        content: "Hi *{{customer_name}}*,\n\nWe've received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**. Your order is now being processed for delivery.\n\nThank you for shopping with us.\n\nBest,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe've received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**. Your order is now being processed for delivery.\n\nThank you for shopping with us.\n\nBest,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount"],
        lastUsed: "",
    },
    {
        id: 28,
        templateId: "b2c_grocery_payment_failed_v2",
        name: "B2C Grocery Payment Failed",
        header: "Payment issue – Lets fix it",
        content: "Hi *{{customer_name}}*,\n\nWe couldn't process the payment for order **#{{order_id}}** (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThanks for your understanding,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe couldn't process the payment for order **#{{order_id}}** (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThanks for your understanding,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "attempted_amount"],
        lastUsed: "",
    },
    {
        id: 29,
        templateId: "b2c_grocery_prepaid_unconfirmed_v2",
        name: "B2C Grocery Prepaid Unconfirmed",
        header: "Order not confirmed – Cancelled",
        content: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**, but we couldn't confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\n\nThanks for your patience,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**, but we couldn't confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\n\nThanks for your patience,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount", "reason", "refund_amount"],
        lastUsed: "",
    },
    {
        id: 30,
        templateId: "b2c_grocery_item_cancelled_v2",
        name: "B2C Grocery Item Cancelled",
        header: "Item cancellation notice",
        content: "Hi *{{customer_name}}*,\n\nWe're sorry to let you know that the item *{{item_name}}* in your order **#{{order_id}}** has been cancelled due to *{{cancel_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* for this item has been processed.{{/if}}\n\nWe appreciate your understanding.\n\nRegards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe're sorry to let you know that the item *{{item_name}}* in your order **#{{order_id}}** has been cancelled due to *{{cancel_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* for this item has been processed.{{/if}}\n\nWe appreciate your understanding.\n\nRegards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "item_name", "order_id", "cancel_reason", "refund_amount"],
        lastUsed: "",
    },
    // B2C Restaurant Templates
    {
        id: 31,
        templateId: "b2c_restaurant_order_placed_confirmed_v2",
        name: "B2C Restaurant Order Confirmed",
        header: "Order confirmed – Bon appetit!",
        content: "Hi *{{customer_name}}*,\n\nThanks so much for your order! Your order **#{{order_id}}** has been confirmed and your delicious meal will be ready by *{{expt_delivery_time}}*.\n\n*Meal Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe can't wait for you to enjoy your meal.\n\nWarm regards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nThanks so much for your order! Your order **#{{order_id}}** has been confirmed and your delicious meal will be ready by *{{expt_delivery_time}}*.\n\n*Meal Details:*\n{{order_details}}\n\nTotal: *₹{{total_amount}}*\n\nWe can't wait for you to enjoy your meal.\n\nWarm regards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "expt_delivery_time", "order_details", "total_amount"],
        lastUsed: "",
    },
    {
        id: 32,
        templateId: "b2c_restaurant_order_out_for_delivery_v2",
        name: "B2C Restaurant Out for Delivery",
        header: "Your meal is on its way!",
        content: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** is now out for delivery and should arrive by *{{delivery_time}}*. We hope you enjoy your meal!\n\nCheers,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** is now out for delivery and should arrive by *{{delivery_time}}*. We hope you enjoy your meal!\n\nCheers,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "delivery_time"],
        lastUsed: "",
    },
    {
        id: 33,
        templateId: "b2c_restaurant_order_delivered_v2",
        name: "B2C Restaurant Order Delivered",
        header: "Enjoy your meal – It's delivered!",
        content: "Hi *{{customer_name}}*,\n\nWe're delighted to let you know that your order **#{{order_id}}** has been delivered. We hope the food is delicious and meets your expectations!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for choosing us. Bon appétit!\n\nBest,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe're delighted to let you know that your order **#{{order_id}}** has been delivered. We hope the food is delicious and meets your expectations!\n\n*Total Paid:* *₹{{paid_amount}}*\n\nThank you for choosing us. Bon appétit!\n\nBest,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount"],
        lastUsed: "",
    },
    {
        id: 34,
        templateId: "b2c_restaurant_order_delivered_credit_v2",
        name: "B2C Restaurant Order Delivered (Credit)",
        header: "Order delivered – Payment on Credit",
        content: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nWe appreciate your patronage and look forward to serving you again soon.\n\nBest,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nYour order **#{{order_id}}** has been delivered on credit. The order amount is *₹{{order_amount}}* and your total pending balance is now *₹{{total_pending_amount}}*.\n\nWe appreciate your patronage and look forward to serving you again soon.\n\nBest,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "order_amount", "total_pending_amount"],
        lastUsed: "",
    },
    {
        id: 35,
        templateId: "b2c_restaurant_order_cancelled_v2",
        name: "B2C Restaurant Order Cancelled",
        header: "Order cancelled",
        content: "Hi *{{customer_name}}*,\n\nWe're sorry to inform you that your order **#{{order_id}}** has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nWe apologize for the inconvenience and hope to serve you better next time.\n\nRegards,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe're sorry to inform you that your order **#{{order_id}}** has been cancelled due to *{{cancellation_reason}}*.\n{{#if refund_amount}}A refund of *₹{{refund_amount}}* has been processed for any payment received.{{/if}}\n\nWe apologize for the inconvenience and hope to serve you better next time.\n\nRegards,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "cancellation_reason", "refund_amount"],
        lastUsed: "",
    },
    {
        id: 36,
        templateId: "b2c_restaurant_payment_success_v2",
        name: "B2C Restaurant Payment Success",
        header: "Payment received – Thank you!",
        content: "Hi *{{customer_name}}*,\n\nWe've received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**. Your meal is now being prepared and will soon be on its way to you.\n\nThank you for dining with us,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe've received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**. Your meal is now being prepared and will soon be on its way to you.\n\nThank you for dining with us,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount"],
        lastUsed: "",
    },
    {
        id: 37,
        templateId: "b2c_restaurant_payment_failed_v2",
        name: "B2C Restaurant Payment Failed",
        header: "Payment issue – Lets fix it",
        content: "Hi *{{customer_name}}*,\n\nWe couldn't process the payment for order **#{{order_id}}** (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThanks for your understanding,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe couldn't process the payment for order **#{{order_id}}** (attempted amount: *₹{{attempted_amount}}*). Kindly try again or let us know if you need any further help.\n\nThanks for your understanding,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "attempted_amount"],
        lastUsed: "",
    },
    {
        id: 38,
        templateId: "b2c_restaurant_prepaid_unconfirmed_v2",
        name: "B2C Restaurant Prepaid Unconfirmed",
        header: "Order not confirmed – Cancelled",
        content: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**, but we couldn't confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\n\nThanks for your patience,\n*{{business_name}}*",
        preview: "Hi *{{customer_name}}*,\n\nWe received your payment of *₹{{paid_amount}}* for order **#{{order_id}}**, but we couldn't confirm the order due to *{{reason}}*. Consequently, the order has been cancelled{{#if refund_amount}} and a refund of *₹{{refund_amount}}* has been processed{{/if}}.\n\nThanks for your patience,\n*{{business_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["customer_name", "business_name", "order_id", "paid_amount", "reason", "refund_amount"],
        lastUsed: "",
    },
    {
        id: 39,
        templateId: "new_support_ticket",
        name: "New Support Request",
        header: "Alert - New Support Request",
        content: "*🚨🚨🚨🚨🚨*\nThe customer has raised a new support request.\n\nTicket Id : *#{{ticket_id}}*\nNetwork Name : *{{network_name}}*\nCustomer id : *#{{customer_id}}*\nCustomer Name : *{{customer_name}}*",
        preview: "*🚨🚨🚨🚨🚨*\nThe customer has raised a new support request.\n\nTicket Id : *#{{ticket_id}}*\nNetwork Name : *{{network_name}}*\nCustomer id : *#{{customer_id}}*\nCustomer Name : *{{customer_name}}*",
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["ticket_id", "network_name", "customer_id", "customer_name"],
        lastUsed: "",
    },
    {
        id: 40,
        templateId: "sales_report_daily",
        name: "Sales Report Daily",
        header: "",
        content: "Hello *{{seller_name}}*,\n\nDate : *{{order_date}}*\n\nEach day brings fresh milestones. Here's your performance snapshot for today: \n\n✅ *Total Orders:* {{total_orders}} recieved _({{cancelled_order}} cancelled, *{{completed_orders}} delivered*)_\n💰 *Total Revenue:* ₹{{total_revenue}}\n🤑 *Net Earnings:* ₹{{total_earnings}}\n\n----\n*Customer Insights*\n- Total Customers : *{{total_customers}}*\n- New Customer : *{{new_customers}}*\n- Repeat Customers : *{{rept_customers}}*\n\nFor a detailed breakdown, please check the attached report. 📊",
        preview: "Hello *{{seller_name}}*,\n\nDate : *{{order_date}}*\n\nEach day brings fresh milestones. Here's your performance snapshot for today: \n\n✅ *Total Orders:* {{total_orders}} recieved _({{cancelled_order}} cancelled, *{{completed_orders}} delivered*)_\n💰 *Total Revenue:* ₹{{total_revenue}}\n🤑 *Net Earnings:* ₹{{total_earnings}}\n\n----\n*Customer Insights*\n- Total Customers : *{{total_customers}}*\n- New Customer : *{{new_customers}}*\n- Repeat Customers : *{{rept_customers}}*\n\nFor a detailed breakdown, please check the attached report. 📊",
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: ["seller_name", "order_date", "total_orders", "cancelled_order", "completed_orders", "total_revenue", "total_earnings", "total_customers", "new_customers", "rept_customers"],
        lastUsed: "",
        footerVariables: ["team_name"],
    },
    {
        id: 41,
        templateId: "logistics_fulfillment_failed",
        name: "Logistics Fulfillment Failed",
        header: "Manual Fulfillment Required",
        content: `Hi Team,\nAn issue occurred with the _prorouting delivery_ for *Order ID: {{order_id}}* .\n\n📦 Please arrange manual fulfilment for the customer at the earliest.\n\n*Order Details*\n- Order id : *{{order_id_2}}*\n- Seller Name : *{{seller_name}}*\n\n*Pick up Details*\n👨‍🍳 Pickup Contact: *{{seller_contact}}*\n🍽 Pickup Address : *{{seller_address}}*\n\n*Drop Details*\n👳‍♂️ Customer Name : *{{customer_name}}*\n📞 Customer Contact: *{{customer_contact}}*\n📍 Delivery Address: *{{delivery_address}}*`,
        preview: `Hi Team,\nAn issue occurred with the _prorouting delivery_ for *Order ID: {{order_id}}* .\n\n📦 Please arrange manual fulfilment for the customer at the earliest.\n\n*Order Details*\n- Order id : *{{order_id_2}}*\n- Seller Name : *{{seller_name}}*\n\n*Pick up Details*\n👨‍🍳 Pickup Contact: *{{seller_contact}}*\n🍽 Pickup Address : *{{seller_address}}*\n\n*Drop Details*\n👳‍♂️ Customer Name : *{{customer_name}}*\n📞 Customer Contact: *{{customer_contact}}*\n📍 Delivery Address: *{{delivery_address}}*`,
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: ["order_id", "order_id_2", "seller_name", "seller_contact", "seller_address", "customer_name", "customer_contact", "delivery_address"],
        lastUsed: "",
    },
    {
        id: 42,
        templateId: "mg_biryani_marketing_v1",
        name: "MG Biryani Marketing",
        header: "Order Directly & Save More",
        content: `Hey {{customer_name}}, great news! 🎉\n\nNow you can order directly from us — *no middlemen, no extra charges!*\n\n🍽️ Enjoy fresh food, faster delivery, and a *flat {{discount_perc}}% OFF* on every direct order!\n\nClick below to grab your discount 👇`,
        preview: `Hey {{customer_name}}, great news! 🎉\n\nNow you can order directly from us — *no middlemen, no extra charges!*\n\n🍽️ Enjoy fresh food, faster delivery, and a *flat {{discount_perc}}% OFF* on every direct order!\n\nClick below to grab your discount 👇`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: ["customer_name", "discount_perc"],
        lastUsed: "",
    },
    {
        id: 43,
        templateId: "b2b_marketing_active_customers",
        name: "B2B Marketing Active Customers",
        header: "Exclusive deals from Farmers Mandi",
        content: `Hi *{{customer_name}}*,\n\n 📋 *Today's offers just for you :*\n{{items}}\n\n✅ Lowest B2B rates in Bengaluru \n💰 No delivery charges \n🚛 On-time delivery from 6 AM to 10 AM\n\n*👇 Tap below to order now!*\n\n📦 For bulk deals/orders: 9036512018`,
        preview: `Hi *{{customer_name}}*,\n\n 📋 *Today's offers just for you :*\n{{items}}\n\n✅ Lowest B2B rates in Bengaluru \n💰 No delivery charges \n🚛 On-time delivery from 6 AM to 10 AM\n\n*👇 Tap below to order now!*\n\n📦 For bulk deals/orders: 9036512018`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix, waTemplateCtas.visit_website],
        phone: "",
        bodyVariables: ["customer_name", "items"],
        lastUsed: "",
    },
    {
        id: 44,
        templateId: "b2b_marketing_inactive_customers",
        name: "B2B Marketing Inactive Customers",
        header: "We miss you!",
        content: `Hi {{customer_name}},\n\n*🛒 Special comeback offer just for you:*\n{{items}}\n\n💥 Bangalore's BEST prices + an EXTRA OFF — for your return!\n💰 No delivery charges \n🚛 On-time delivery from 6 AM to 10 AM\n\n*👇 Tap the button below to redeem your deal now.*\n\n🤝 Need help? Call us @ 9036512018`,
        preview: `Hi {{customer_name}},\n\n*🛒 Special comeback offer just for you:*\n{{items}}\n\n💥 Bangalore's BEST prices + an EXTRA OFF — for your return!\n💰 No delivery charges \n🚛 On-time delivery from 6 AM to 10 AM\n\n*👇 Tap the button below to redeem your deal now.*\n\n🤝 Need help? Call us @ 9036512018`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix, waTemplateCtas.visit_website],
        phone: "",
        bodyVariables: ["customer_name", "items"],
        lastUsed: "",
    },
    {
        id: 45,
        templateId: "mg_donne_wa_marketing_v2_registration",
        name: "MG Donne Marketing Registration",
        header: "",
        content: `Hey there! 👋, \n\nGet flat *30% OFF* when you order directly from us — *no middlemen, no extra charges!* \nFresh, hot food delivered straight to your doorstep. \n\nTap *Hello* below to register and grab your discount 🔥`,
        preview: `Hey there! 👋, \n\nGet flat *30% OFF* when you order directly from us — *no middlemen, no extra charges!* \nFresh, hot food delivered straight to your doorstep. \n\nTap *Hello* below to register and grab your discount 🔥`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.hello],
        phone: "",
        bodyVariables: [],
        headerVariables: ["image_url"],
        lastUsed: "",
    },
    {
        id: 46,
        templateId: "mg_donne_wa_marketing_v2",
        name: "MG Donne Marketing V2",
        header: "",
        content: `Hey {{customer_name}}, great news! 🎉\n\nGet flat *{{discount_perc}}% OFF* when you order directly from us — *no middlemen, no extra charges!*\n🍽️ Fresh food delivered straight to your doorstep. \n\nClick below to grab your discount 👇`,
        preview: `Hey {{customer_name}}, great news! 🎉\n\nGet flat *{{discount_perc}}% OFF* when you order directly from us — *no middlemen, no extra charges!*\n🍽️ Fresh food delivered straight to your doorstep. \n\nClick below to grab your discount 👇`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: ["customer_name", "discount_perc"],
        headerVariables: ["image_url"],
        lastUsed: "",
    },
    {
        id: 47,
        templateId: "mg_launch_half50",
        name: "MG Launch Half 50 Offer",
        header: "",
        content: `*🤩 WEEKEND MADNESS starts now! 🤩*\n\nUse code *HALF50* for *50 % OFF every dish*\n- no minimum order, \n- *FREE delivery*, \n- zero hidden fees. \n\nEat the real Donne biryani you love at half price before we sell out. 🔥`,
        preview: `*🤩 WEEKEND MADNESS starts now! 🤩*\n\nUse code *HALF50* for *50 % OFF every dish*\n- no minimum order, \n- *FREE delivery*, \n- zero hidden fees. \n\nEat the real Donne biryani you love at half price before we sell out. 🔥`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: [],
        headerVariables: ["image_url"],
        lastUsed: "",
        footerVariables: [],
    },
    {
        id: 48,
        templateId: "mg_social_proof",
        name: "MG Social Proof",
        header: "475+ bowls gone already",
        content: `Bengaluru can't stop 🤩  Code *HALF50* still halves every price, *FREE delivery*, no caps, no fine print. \n\nTap Order Now and join the feast.`,
        preview: `Bengaluru can't stop 🤩  Code *HALF50* still halves every price, *FREE delivery*, no caps, no fine print. \n\nTap Order Now and join the feast.`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: [],
        lastUsed: "",
        footerVariables: [],
    },
    {
        id: 49,
        templateId: "mg_missed_launch",
        name: "MG Missed Launch",
        header: "",
        content: `Hi there👋,   \nWe couldn't reach you yesterday. \nFrom *11:30 AM* today ,our kitchen fires up 🔥 with code *HALF50*—\n- flat *50 % OFF every dish*,\n- no hidden fees,\n- *FREE delivery*\n \nWe'll ping you the moment the burners light.`,
        preview: `Hi there👋,   \nWe couldn't reach you yesterday. \nFrom *11:30 AM* today ,our kitchen fires up 🔥 with code *HALF50*—\n- flat *50 % OFF every dish*,\n- no hidden fees,\n- *FREE delivery*\n \nWe'll ping you the moment the burners light.`,
        type: "WhatsApp",
        ctas: [],
        phone: "",
        bodyVariables: [],
        headerVariables: ["image_url"],
        lastUsed: "",
        footerVariables: [],
    },
    {
        id: 50,
        templateId: "mg_final_day_open",
        name: "MG Final Day Open",
        header: "",
        content: `Hi there 👋, *Last Day!*\nWe're OPEN! *HALF50 = 50 % off + free delivery*. \n\n.....*1738+ bowls sold*.....\n\nOrder Now 👇`,
        preview: `Hi there 👋, *Last Day!*\nWe're OPEN! *HALF50 = 50 % off + free delivery*. \n\n.....*1738+ bowls sold*.....\n\nOrder Now 👇`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: [],
        headerVariables: ["image_url"],
        lastUsed: "",
        footerVariables: [],
    },
    {
        id: 51,
        templateId: "last_chance__50__off_ends_23__59",
        name: "Last Chance 50% Off Ends Tonight",
        header: "LAST CHANCE – 50 % OFF ends tonight",
        content: `Hello 👋 ,\nIt's now or never! Code *HALF50* slashes the whole menu by *50%, FREE delivery* & no hidden fees.\n\n*1938+ bowls sold already—*\nTap Order Now and enjoy authentic Donne biryani before the kitchen closes tonight. 🔥`,
        preview: `Hello 👋 ,\nIt's now or never! Code *HALF50* slashes the whole menu by *50%, FREE delivery* & no hidden fees.\n\n*1938+ bowls sold already—*\nTap Order Now and enjoy authentic Donne biryani before the kitchen closes tonight. 🔥`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: [],
        lastUsed: "",
        footerVariables: [],
    },
    {
        id: 52,
        templateId: "mg_second_chance_donne30",
        name: "MG Second Chance Donne 30% Off",
        header: "Missed 50% off? Try 30% OFF",
        content: `You probably saw the buzz—*2053 bowls vanished in 48h* and _latecomers were left empty-handed_ 😬. \n*DONNE30 → 30% off, no limits.*\nUse *DONNE30* anytime this week and slice 30% off every dish _(no caps, no hidden fees)._ \n\nTap _Order Now_ while the cauldrons are still hot.`,
        preview: `You probably saw the buzz—*2053 bowls vanished in 48h* and _latecomers were left empty-handed_ 😬. \n*DONNE30 → 30% off, no limits.*\nUse *DONNE30* anytime this week and slice 30% off every dish _(no caps, no hidden fees)._ \n\nTap _Order Now_ while the cauldrons are still hot.`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: [],
        lastUsed: "",
        footerVariables: [],
    },
    {
        id: 53,
        templateId: "mg_thank_you_donne30",
        name: "MG Thank You Donne 30% Off",
        header: "Thank you! Your 30 % off code inside",
        content: `Hey {{customer_name}}, you packed our kitchen with love—over *2053 bowls* served 🥰. \n\n*DONNE30 → 30% off all week!*\n\nWe're genuinely grateful. As a small thank-you, just use *DONNE30* at checkout any day this week and enjoy 30% off on every dish.\n\nTap _Order Again_ whenever the craving hits—we'd be honoured to cook for you again.`,
        preview: `Hey {{customer_name}}, you packed our kitchen with love—over *2053 bowls* served 🥰. \n\n*DONNE30 → 30% off all week!*\n\nWe're genuinely grateful. As a small thank-you, just use *DONNE30* at checkout any day this week and enjoy 30% off on every dish.\n\nTap _Order Again_ whenever the craving hits—we'd be honoured to cook for you again.`,
        type: "WhatsApp",
        ctas: [waTemplateCtas.url_suffix],
        phone: "",
        bodyVariables: ["customer_name"],
        lastUsed: "",
        footerVariables: [],
    },
];

export class TemplateService {
    private validateRequest(request: SendTemplateRequest): ValidatedTemplateRequest {
        return SendTemplateRequestSchema.parse(request);
    }

    private extractVariables(variables: string[], variablesObject?: { [key: string]: string }): string[] {
        return variables.map(variable => variablesObject?.[variable] || '');
    }

    private getTemplate(templateId: string): Template | undefined {
        return templates.find(template => template.templateId === templateId);
    }

    private createButtonComponents(ctas: WaTemplateCta[], buttonVariables?: { [key: string]: string }): ButtonComponent[] {  
        return ctas.map((cta, index) => ({
            type: 'button' as const,
            sub_type: cta.type,
            index: index.toString(),
            parameters: [{
                type: 'payload' as const,
                payload: buttonVariables?.[cta.value] || cta.value
            }]
        }));
    }

    private createHeaderComponent(template: Template, headerVariables?: { [key: string]: string }) {
        if (!template.headerVariables) return null;

        if (template.headerVariables.includes("image_url")) {
            const imageUrl = headerVariables?.image_url;
            if (!imageUrl) return null;
            
            return {
                type: 'header' as const,
                parameters: [{
                    type: 'image' as const,
                    // parameter_name: 'image_url',
                    image: {
                        link: imageUrl
                    }
                }]
            }
        }

        let variables: string[] = [];

        if (template.headerVariables && headerVariables) {
            variables = this.extractVariables(template.headerVariables, headerVariables);
        }

        return {
            type: 'header' as const,
            parameters: template.headerVariables.map((varName, index) => {
                const variable = variables[index] || '';
                if (varName.includes("image_url")) {
                    if (!variable) return null;
                    return {
                        type: 'image' as const,
                        // parameter_name: varName,
                        image: {
                            link: variable
                        }
                    }
                } else {
                    return {
                        type: 'text' as const,
                        parameter_name: varName,
                        text: variable
                    }
                }
            }).filter((param): param is NonNullable<typeof param> => param !== null)
        };
    }

    private createBodyComponent(template: Template, bodyVariables?: { [key: string]: string }) {
        let templateVariables = template.bodyVariables || [];
        let variables: string[] = [];

        if (templateVariables && bodyVariables) {
            variables = this.extractVariables(templateVariables, bodyVariables);
        }

        return {
            type: 'body' as const,
            parameters: templateVariables.map((varName, index) => ({
                type: 'text' as const,
                parameter_name: varName,
                text: variables[index] || ''
            }))
        };
    }

    private createFooterComponent(template: Template, footerVariables?: { [key: string]: string }) {
        if (!template.footerVariables) return null;

        let footerText = "";
        if (footerVariables) {
            Object.entries(footerVariables).forEach(([key, value]) => {
                footerText = footerText.replace(`{{${key}}}`, value);
            });
        }

        return {
            type: 'footer' as const,
            parameters: [{
                type: 'text' as const,
                text: footerText
            }]
        };
    }

    public createWhatsAppTemplate(request: SendTemplateRequest): WhatsAppTemplate {
        // Validate request
        const validatedRequest = this.validateRequest(request);
        
        const template = this.getTemplate(validatedRequest.templateId);
        if (!template) {
            throw new Error(`Template with ID ${validatedRequest.templateId} not found`);
        }

        const components = [];

        // Add header if exists
        const headerComponent = this.createHeaderComponent(template, validatedRequest.variables.header);
        if (headerComponent) {
            components.push(headerComponent);
        }

        // Add body
        const bodyComponent = this.createBodyComponent(template, validatedRequest.variables.body);
        components.push(bodyComponent);

        // Add footer if exists
        const footerComponent = this.createFooterComponent(template, validatedRequest.variables.footer);
        if (footerComponent) {
            components.push(footerComponent);
        }

        // Add buttons if exist
        if (template.ctas && template.ctas.length > 0) {
            components.push(...this.createButtonComponents(template.ctas, validatedRequest.variables.button));
        }

        return {
            name: template.templateId,
            language: { code: 'en' }, // You might want to make this configurable
            components
        };
    }

    public getAvailableTemplates() {
        return templates;
    }

    /**
     * Converts a WhatsApp template format to internal Template format
     */
    public convertWhatsAppTemplateToTemplate(
        whatsappTemplate: WhatsAppTemplateInput, 
        templateName?: string,
        nextId?: number
    ): Template {
        const headerComponent = whatsappTemplate.components.find(c => c.type === 'HEADER');
        const bodyComponent = whatsappTemplate.components.find(c => c.type === 'BODY');
        const footerComponent = whatsappTemplate.components.find(c => c.type === 'FOOTER');
        const buttonComponent = whatsappTemplate.components.find(c => c.type === 'BUTTONS');

        // Extract variables from text (looking for {{variable}} patterns)
        const extractVariables = (text: string): string[] => {
            const matches = text.match(/\{\{([^}]+)\}\}/g);
            return matches ? matches.map(match => match.replace(/[{}]/g, '')) : [];
        };

        // Get body content and variables
        const bodyText = bodyComponent?.text || '';
        const bodyVariables = extractVariables(bodyText);

        // Get header variables
        let headerVariables: string[] = [];
        let headerText = '';
        if (headerComponent) {
            if (headerComponent.format === 'IMAGE') {
                headerVariables = ['image_url'];
            } else if (headerComponent.text) {
                headerText = headerComponent.text;
                headerVariables = extractVariables(headerText);
            }
        }

        // Get footer variables
        let footerVariables: string[] = [];
        if (footerComponent?.text) {
            footerVariables = extractVariables(footerComponent.text);
        }

        // Map buttons to CTAs
        const ctas: WaTemplateCta[] = [];
        if (buttonComponent?.buttons) {
            buttonComponent.buttons.forEach((button, index) => {
                if (button.type === 'URL') {
                    ctas.push(waTemplateCtas.url_suffix);
                } else if (button.type === 'QUICK_REPLY') {
                    // Try to map to existing CTAs based on button text
                    const buttonText = button.text.toLowerCase();
                    if (buttonText.includes('pay')) {
                        ctas.push(waTemplateCtas.pay_dues);
                    } else if (buttonText.includes('order') || buttonText.includes('shop')) {
                        ctas.push(waTemplateCtas.place_update_order);
                    } else if (buttonText.includes('help') || buttonText.includes('contact')) {
                        ctas.push(waTemplateCtas.help);
                    } else if (buttonText.includes('hello')) {
                        ctas.push(waTemplateCtas.hello);
                    } else {
                        // Create a generic quick reply CTA
                        ctas.push({
                            id: Object.keys(waTemplateCtas).length + index + 1,
                            label: button.text,
                            value: button.text.toLowerCase().replace(/\s+/g, '_'),
                            type: 'quick_reply'
                        });
                    }
                }
            });
        }

        // Generate next available ID
        const id = nextId || Math.max(...templates.map(t => t.id)) + 1;

        const template: Template = {
            id,
            templateId: whatsappTemplate.name,
            name: templateName || whatsappTemplate.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            header: headerText,
            content: bodyText,
            preview: bodyText,
            type: "WhatsApp",
            ctas,
            phone: "",
            bodyVariables,
            headerVariables: headerVariables.length > 0 ? headerVariables : undefined,
            footerVariables: footerVariables.length > 0 ? footerVariables : undefined,
            lastUsed: "",
        };

        return template;
    }

    /**
     * Adds a converted WhatsApp template to the templates array
     */
    public addWhatsAppTemplate(
        whatsappTemplate: WhatsAppTemplateInput, 
        templateName?: string
    ): Template {
        const convertedTemplate = this.convertWhatsAppTemplateToTemplate(whatsappTemplate, templateName);
        templates.push(convertedTemplate);
        return convertedTemplate;
    }
}

export const templateService = new TemplateService(); 
