// routes/index.ts

import {Express, request} from 'express';
import {getWhatsAppTemplates, sendWhatsAppMessage, sendWhatsAppTemplateMessage} from '@services/whatsappService.js';
import whatsappWebhookRoute from './whatsappWebhookRoute.js';
import {WhatsAppMessageData, SendTemplateRequest} from "@/types/whatsapp.js";
import {
    createRestaurantTemplateRequestFromEvent,
    createSupportTicketRequest,
    createDayWiseReportRequest,
    handleOrderCancelled, handleOrderDeliveredPaid, handleOrderDeliveredPaymentPending,
    handleOrderPlaced,
    handleSellerOrderPlaced,
    handleTripDispatched,
    createLogisticsFulfillmentFailedRequest
} from "@utils/eventHandlers.js";
import {BuyerEventData, EventRequest, SellerEventData, EventType, TripOrderEventData} from "@/types/event.js";
import NotificationRoute from './notification.js';
import { logRequestResponse } from '@middleware/requestLogger.middleware.js';
import requestContext from '@middleware/context.middleware.js';
import WaWebSessionRoute from './waWebSession.route.js';
import TemplateMessageRoute from './templateMessage.route.js';
import { templateService } from '@/services/templateService.js';


export const configureRoutes = (app: Express, authenticateToken: any) => {


    app.get('/api/hi', (req, res) => {
        res.send(`Hello there!`);
    });
    app.get('/api/protected', authenticateToken, (req, res) => {
        res.json({message: 'This is a protected route', data: 'Some sensitive data'});
    });

    app.post('/api/whatsapp/sendmessage', authenticateToken, async (req, res) => {
        const {phoneNo, templateName} = req.body;

        if (!phoneNo || !templateName) {
            return res.status(400).json({error: 'phoneNo and templateName are required'});
        }

        try {
            const result = await sendWhatsAppMessage({phoneNo, templateName, templateValues: []});
            res.json(result);
        } catch (error) {
            res.status(500).json(error);
        }
    });


    app.post('/api/whatsapp/send-template-message', authenticateToken, async (req, res) => {
        const {phoneNo, templateName, templateValues} = req.body;
        console.log("Received request to send message to", phoneNo, "using template", templateName);

        if (!phoneNo || !templateName) {
            console.log("Missing required parameters");
            return res.status(400).json({error: 'phoneNo and templateName are required'});
        }

        try {
            console.log("Sending WhatsApp message 2...");

            const result = await sendWhatsAppMessage({
                phoneNo,
                templateName,
                templateValues: (templateValues && templateValues.length > 0) ? templateValues : []
            });
            console.log("WhatsApp message sent successfully:", result);
            res.json(result);
            return res.status(200)
        } catch (error) {
            console.log("Failed to send WhatsApp message:", error);
            console.error('Error sending WhatsApp message:', error);
            res.status(500).json({
                error: 'Failed to send WhatsApp message',
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });


    app.get('/api/whatsapp/getalltemplates', authenticateToken, async (req, res) => {
        console.log("Received request to get all WhatsApp templates");


        try {
            console.log("Fetching WhatsApp templates...");

            const result = await getWhatsAppTemplates();
            res.json(result);
        } catch (error) {
            res.status(500).json({
                details: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    })


    app.post('/api/whatsapp/triggerevent', authenticateToken, async (req, res) => {
        try {
            const { eventData, eventType, targetType, targetWANumber, wabPhoneNumberId, wabToken }: EventRequest = req.body;
            console.log("sasasasa Received event:", eventType, JSON.stringify(eventData));
            const mobileNumbers = targetWANumber?.trim().split(',') || [];
            let messageDataArray: WhatsAppMessageData[] = [];

            const v2MessageDataArray: SendTemplateRequest[] = [];

            switch (targetType) {
                case 'mnet_seller':
                    switch (eventType) {
                        case 'OrderPlaced':
                            const messages = mobileNumbers.map( (number) => 
                                 handleSellerOrderPlaced(number, eventData as TripOrderEventData)
                            ) || []
                            messageDataArray.push(...messages);
                            break;
                        case "DailySalesReport": 
                            const templateRequest = createDayWiseReportRequest(req.body);
                                if (templateRequest) {
                                    const template = templateService.createWhatsAppTemplate(templateRequest);
                                    let results: any[] = [];
                                    if(mobileNumbers.length > 0 ) {
                                        results = await Promise.allSettled(mobileNumbers.map(async (number) => {
                                            return await sendWhatsAppTemplateMessage({
                                                targetPhoneNumber: number,
                                                wabPhoneNumberId: templateRequest.wabPhoneNumberId,
                                                accessToken: templateRequest.accessToken,
                                                template
                                            });
                                        }));
                                    }
                                    return res.json(results);
                                }
                            break;
                        default:
                            return res.status(400).json({ error: 'Unsupported seller event type' });
                    }
                    break;

                case 'fm_buyer':
                    switch (eventType) {
                        case 'OrderPlaced':
                            messageDataArray = [handleOrderPlaced(eventData as BuyerEventData)];
                            break;
                        case 'OrderDispatched':
                            messageDataArray = handleTripDispatched({ orders: [eventData as BuyerEventData] });
                            break;
                        case 'OrderCancelled':
                            messageDataArray = [handleOrderCancelled(eventData as BuyerEventData)];
                            break;
                        case 'OrderDelivered':
                            messageDataArray = [handleOrderDeliveredPaid(eventData as BuyerEventData)];
                            break;
                        case 'OrderDeliveredWithCredit':
                            messageDataArray = [handleOrderDeliveredPaymentPending(eventData as BuyerEventData)];
                            break;
                        case 'PaymentReceived':
                            // messageDataArray = [handlePaymentReceived(eventData as BuyerEventData)];
                            break;
                        default:
                            return res.status(400).json({ error: 'Unsupported buyer event type' });
                    }

                case 'wab_buyer':
                    if(req.body.eventData.networkType === 'B2C' && req.body.eventData.ondcDomain === 'RET10') {
                        switch (eventType) {
                            case 'OrderPlaced':
                                messageDataArray = [
                                    {
                                        ...handleOrderPlaced(eventData as BuyerEventData),
                                        phoneNumberId: wabPhoneNumberId,
                                        accessToken: wabToken
                                    }
                                ];
                                break;
                                case 'OrderDispatched':
                                    messageDataArray = handleTripDispatched({ orders: [eventData as BuyerEventData] }).map(message => ({
                                        ...message,
                                        phoneNumberId: wabPhoneNumberId,
                                        accessToken: wabToken
                                    }));
                                    break;
                                case 'OrderCancelled':
                                    messageDataArray = [
                                        {
                                            ...handleOrderCancelled(eventData as BuyerEventData),
                                            phoneNumberId: wabPhoneNumberId,
                                            accessToken: wabToken
                                        }
                                    ];
                                    break;
                                case 'OrderDelivered':
                                    messageDataArray = [
                                        {
                                            ...handleOrderDeliveredPaid(eventData as BuyerEventData),
                                            phoneNumberId: wabPhoneNumberId,
                                            accessToken: wabToken
                                        }
                                    ];

                                    break;
                                case 'OrderDeliveredWithCredit':
                                    messageDataArray = [
                                        {
                                            ...handleOrderDeliveredPaymentPending(eventData as BuyerEventData),
                                            phoneNumberId: wabPhoneNumberId,
                                            accessToken: wabToken
                                        }
                                    ];
                                    break;
                            default:
                                return res.status(400).json({ error: 'Unsupported WAB buyer event type' });
                    }
                }
                    break;

                case 'mnet_ops':
                    switch (eventType) {
                        case 'NewSupportTicket':
                            const templateRequest = createSupportTicketRequest(req.body);
                            if (templateRequest) {
                                const template = templateService.createWhatsAppTemplate(templateRequest);
                                let results: any[] = [];
                                if(mobileNumbers.length > 0 ) {
                                    results = await Promise.all(mobileNumbers.map(async (number) => {
                                        return await sendWhatsAppTemplateMessage({
                                            targetPhoneNumber: number,
                                            wabPhoneNumberId: templateRequest.wabPhoneNumberId,
                                            accessToken: templateRequest.accessToken,
                                            template
                                        });
                                    }));
                                }
                                return res.json(results);
                            }
                        case 'RiderNotFound':
                            const logisticsFulfillmentFailedRequest = createLogisticsFulfillmentFailedRequest(req.body);
                            if (logisticsFulfillmentFailedRequest) {
                                const template = templateService.createWhatsAppTemplate(logisticsFulfillmentFailedRequest);
                                let results: any[] = [];
                                if(mobileNumbers.length > 0 ) {
                                    results = await Promise.all(mobileNumbers.map(async (number) => {
                                        return await sendWhatsAppTemplateMessage({
                                            targetPhoneNumber: number,
                                            wabPhoneNumberId: logisticsFulfillmentFailedRequest.wabPhoneNumberId,
                                            accessToken: logisticsFulfillmentFailedRequest.accessToken,
                                            template
                                        });
                                    }));
                                }
                                return res.json(results);
                            }
                        default:
                            return res.status(400).json({ error: 'Unsupported ops event type' });
                    }
                    break;

                default:
                    return res.status(400).json({ error: 'Unsupported target type' });
            }

            if(targetType === 'wab_buyer' && req.body.eventData.networkType === 'B2C' && req.body.eventData.ondcDomain === 'RET11'){
                const sendTemplateMessageRequest = createRestaurantTemplateRequestFromEvent(req.body);
                if(sendTemplateMessageRequest){
                    // Create WhatsApp template message
                    const template = templateService.createWhatsAppTemplate(sendTemplateMessageRequest);

                    // Send the template message
                    const result = await sendWhatsAppTemplateMessage({
                        targetPhoneNumber: sendTemplateMessageRequest.targetPhoneNumber,
                        wabPhoneNumberId: sendTemplateMessageRequest.wabPhoneNumberId,
                        accessToken: sendTemplateMessageRequest.accessToken,
                        template
                    });
                    return res.json(result);
                }
            }

            if (messageDataArray.length > 0) {
                const results = await Promise.all(messageDataArray.map(sendWhatsAppMessage));
                res.json(results);
            } else {
                res.status(500).json({ error: 'Failed to process event' });
            }
        } catch (error) {
            console.error("Error triggering event:", error);
            res.status(500).json({ error: 'Internal server error' });
        }
    });

    const notificationRoute = new NotificationRoute();
    const waWebSessionRoute = new WaWebSessionRoute();
    const templateMessageRoute = new TemplateMessageRoute();
    app.use('/api/whatsappwebhook',requestContext ,logRequestResponse,whatsappWebhookRoute);
    app.use("/",requestContext ,logRequestResponse, notificationRoute.router)
    app.use("/",requestContext ,logRequestResponse, waWebSessionRoute.router)
    app.use("/", requestContext, logRequestResponse, templateMessageRoute.router);
};
