import {
  __commonJS
} from "./chunk-N5SXXOWC.js";

// browser-external:node:util
var require_node_util = __commonJS({
  "browser-external:node:util"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:util" has been externalized for browser compatibility. Cannot access "node:util.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:events
var require_node_events = __commonJS({
  "browser-external:node:events"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:events" has been externalized for browser compatibility. Cannot access "node:events.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

export {
  require_node_util,
  require_node_events
};
//# sourceMappingURL=chunk-FG6CHLFO.js.map
