import { WhatsAppMessageData, SendTemplateRequest, ComponentVariables } from "@/types/whatsapp.js";
import { BuyerEventData, SellerEventData, TripOrderEventData, TripOrderDetailEventData, EventRequest, EventType, SupportTicketEventData, WabDayWiseReportEventData } from "@/types/event.js";

interface EventData {
  buyerName: string;
  orderGroupId: number;
  totalAmount: number;
  primaryContactNumber: string;
  [key: string]: any;
}
export const handleOrderPlaced = (
  eventData: BuyerEventData
): WhatsAppMessageData => {
  const orderDetails = eventData.orderDetail
    .map((item) => `• ${item.itemName} + ${item.qty} ${item.unit}`)
    .join(", ");

  return {
    phoneNo: eventData.primaryContactNumber,
    templateName: "f_business_order_confirm",
    templateValues: [
      eventData.buyerName,
      orderDetails,
      eventData.orderGroupId.toString(),
      eventData.totalAmount.toString(),
    ],
  };
};


export const handleSellerOrderPlaced = (
  targetWANumber: string,
  eventData: TripOrderEventData
): WhatsAppMessageData => {
  return {
    phoneNo: targetWANumber,
    templateName: "mnet_seller_order_placed",
    templateValues: [
      eventData.orderGroupId.toString(),
      eventData.totalAmount.toString(),
      eventData.sellerName,
      eventData.buyerName,
    ],
  };
};

export const handleOrderCancelled = (
  eventData: BuyerEventData
): WhatsAppMessageData => ({
  phoneNo: eventData.primaryContactNumber,
  templateName: "complete_order_cancellation",
  templateValues: [eventData.buyerName, eventData.orderGroupId.toString()],
});

export const handleTripDispatched = (eventData: {
  orders: BuyerEventData[];
}): WhatsAppMessageData[] =>
  eventData.orders.map((order) => ({
    phoneNo: order.primaryContactNumber,
    templateName: "out_for_delivery",
    templateValues: [
      order.buyerName,
      order.orderGroupId.toString(),
      order.totalAmount.toString(),
    ],
  }));

export const handleOrderDeliveredPaid = (
  eventData: BuyerEventData
): WhatsAppMessageData => ({
  phoneNo: eventData.primaryContactNumber,
  templateName: "delivery_completed",
  templateValues: [
    eventData.buyerName,
    eventData.orderGroupId.toString(),
    eventData.totalAmount.toString(),
  ],
});

export const handleOrderDeliveredPaymentPending = (
  eventData: BuyerEventData
): WhatsAppMessageData => ({
  phoneNo: eventData.primaryContactNumber,
  templateName: "delivery_confirmation_with_credit",
  templateValues: [
    eventData.buyerName,
    eventData.orderGroupId.toString(),
    eventData.orderAmount.toString(),
    eventData.totalAmount.toString(),
  ],
});

/**
 * Utility functions to create SendTemplateRequest objects for B2C restaurant templates
 */

// Helper function to format order details
const formatOrderDetailsForRestaurant = (orderDetails: TripOrderDetailEventData[]): string => {
  return orderDetails
    .map((item) => `• ${item.itemName} x ${item.qty} - ₹${item.amount}`)
    .join("\n");
};

// Helper function to get expected delivery time
const getExpectedDeliveryTime = (eventData: TripOrderEventData): string => {
  return eventData.estDeliveryTime || "soon";
};

// Type guard to check if eventData is TripOrderEventData
const isTripOrderEventData = (eventData: any): eventData is TripOrderEventData => {
  return eventData && 'orderDetail' in eventData && Array.isArray(eventData.orderDetail);
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_order_placed_confirmed_v2 template
 */
export const createRestaurantOrderConfirmedRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "expt_delivery_time": getExpectedDeliveryTime(eventData),
      "order_details": formatOrderDetailsForRestaurant(eventData.orderDetail),
      "total_amount": eventData.totalAmount.toString()
    }
  };

  return {
    templateId: "b2c_restaurant_order_placed_confirmed_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_order_out_for_delivery_v2 template
 */
export const createRestaurantOutForDeliveryRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  const deliveryTime = getExpectedDeliveryTime(eventData);
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "delivery_time": deliveryTime
    }
  };

  return {
    templateId: "b2c_restaurant_order_out_for_delivery_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_order_delivered_v2 template
 */
export const createRestaurantOrderDeliveredRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "paid_amount": eventData.totalAmount.toString()
    }
  };

  return {
    templateId: "b2c_restaurant_order_delivered_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_order_delivered_credit_v2 template
 */
export const createRestaurantOrderDeliveredCreditRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  const totalPendingAmount = eventData.creditPendingAmount?.toString() || "0";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "order_amount": eventData.totalAmount.toString(),
      "total_pending_amount": totalPendingAmount
    }
  };

  return {
    templateId: "b2c_restaurant_order_delivered_credit_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_order_cancelled_v2 template
 */
export const createRestaurantOrderCancelledRequest = (
  eventRequest: EventRequest,
  cancellationReason: string = "unavailability of items",
  refundAmount: string = "0"
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "cancellation_reason": cancellationReason,
      "refund_amount": refundAmount
    }
  };

  return {
    templateId: "b2c_restaurant_order_cancelled_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_payment_success_v2 template
 */
export const createRestaurantPaymentSuccessRequest = (
  eventRequest: EventRequest,
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "paid_amount": eventData.totalAmount.toString()
    }
  };

  return {
    templateId: "b2c_restaurant_payment_success_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_payment_failed_v2 template
 */
export const createRestaurantPaymentFailedRequest = (
  eventRequest: EventRequest,
  attemptedAmount: string
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "attempted_amount": attemptedAmount
    }
  };

  return {
    templateId: "b2c_restaurant_payment_failed_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the b2c_restaurant_prepaid_unconfirmed_v2 template
 */
export const createRestaurantPrepaidUnconfirmedRequest = (
  eventRequest: EventRequest,
  paidAmount: string,
  reason: string = "unavailability of items",
  refundAmount?: string
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  const businessName = eventData.sellerName || "Restaurant";
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "customer_name": eventData.buyerName,
      "business_name": businessName,
      "order_id": eventData.orderGroupId.toString(),
      "paid_amount": paidAmount,
      "reason": reason
    }
  };

  if (refundAmount) {
    variables.body!["refund_amount"] = refundAmount;
  }

  return {
    templateId: "b2c_restaurant_prepaid_unconfirmed_v2",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the new_support_ticket template
 */
export const createSupportTicketRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  const eventData = eventRequest.eventData as SupportTicketEventData;
  
  if (!eventData || !eventData.ticketId) {
    return null;
  }
  
  const variables: ComponentVariables = {
    body: {
      "ticket_id": eventData.ticketId.toString(),
      "network_name": eventData.sellerName || "mnet",
      "customer_id": eventData.userId.toString(),
      "customer_name": eventData.userName
    }
  };

  return {
    templateId: "new_support_ticket",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the sales_report_daily template
 */
export const createDayWiseReportRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  const eventData = eventRequest.eventData as WabDayWiseReportEventData;
  
  if (!eventData || !eventData.sellerId) {
    return null;
  }
  
  const variables: ComponentVariables = {
    body: {
      "seller_name": eventData.sellerName,
      "order_date": eventData.deliveryDate,
      "total_orders": eventData.totalOrders.toString(),
      "cancelled_order": eventData.cancelledOrders.toString(),
      "completed_orders": eventData.deliveredOrders.toString(),
      "total_revenue": eventData.totalRevenue.toString(),
      "total_earnings": eventData.totalEarnings.toString(),
      "total_customers": eventData.totalCustomers.toString(),
      "new_customers": eventData.newCustomers.toString(),
      "rept_customers": eventData.repeatCustomers.toString()
    },
    button: {
      "url_suffix": `/open/reports/restaurant-daily-report?seller_id=${eventData.sellerId}&order_date=${eventData.deliveryDate}&seller_name=${eventData.sellerName}`
    },
    footer: {
      "team_name": "Team mNet"
    }
  };

  return {
    templateId: "sales_report_daily",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates a SendTemplateRequest for the logistics_fulfillment_failed template
 */
export const createLogisticsFulfillmentFailedRequest = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;
  
  const variables: ComponentVariables = {
    header: {},
    body: {
      "order_id": eventData.orderGroupId.toString(),
      "order_id_2": eventData.orderGroupId.toString(),
      "seller_name": eventData.sellerName,
      "seller_contact": eventData.salesExecutiveMobile,
      "seller_address": "Order Details",
      "customer_name": eventData.buyerName,
      "customer_contact": eventData.primaryContactNumber,
      "delivery_address": "Order Details"
    }
  };

  return {
    templateId: "logistics_fulfillment_failed",
    variables,
    targetPhoneNumber: eventRequest.targetWANumber,
    wabPhoneNumberId: eventRequest.wabPhoneNumberId,
    accessToken: eventRequest.wabToken
  };
};

/**
 * Creates the appropriate B2C restaurant template request based on event type
 */
export const createRestaurantTemplateRequestFromEvent = (
  eventRequest: EventRequest
): SendTemplateRequest | null => {
  if (!isTripOrderEventData(eventRequest.eventData)) {
    return null;
  }

  const eventData = eventRequest.eventData;

  switch (eventRequest.eventType) {
    case "OrderPlaced":
      return createRestaurantOrderConfirmedRequest(eventRequest);
    
    case "OrderDispatched":
      return createRestaurantOutForDeliveryRequest(eventRequest);
    
    case "OrderDelivered":
      return createRestaurantOrderDeliveredRequest(eventRequest);
    
    case "OrderDeliveredWithCredit":
      return createRestaurantOrderDeliveredCreditRequest(eventRequest);
    
    case "OrderCancelled":
      return createRestaurantOrderCancelledRequest(eventRequest);
    
    case "PaymentReceived":
      return createRestaurantPaymentSuccessRequest(eventRequest);
    
    default:
      return null;
  }
};
