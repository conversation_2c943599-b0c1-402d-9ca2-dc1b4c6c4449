import { json } from "@remix-run/node";
import { ApiResponse } from "~/types/api/Api";
import {
  NetworkBanner,
  Networks,
} from "~/types/api/businessConsoleService/Network";
import {
  NetWorkAreaDetails,
  NetWorkDetails,
} from "~/types/api/businessConsoleService/netWorkinfo";
import { networkAgents } from "~/types/api/businessConsoleService/SellerManagement";
import { NetworkTheme } from "~/types/api/common";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export async function getNetWorkSeller(
  netWorkId: number,
  request?: Request
): Promise<ApiResponse<NetWorkDetails[]>> {
  return apiRequest<NetWorkDetails[]>(
    `${API_BASE_URL}/bc/mnetadmin/network/${netWorkId}/seller`,
    "GET",
    undefined,
    {},
    true,
    request
  );
}

export async function createNetwork(
  requestBody: any,
  request?: Request
): Promise<ApiResponse<Networks>> {
  console.log(request, "createNeworkData");
  const response = apiRequest<Networks>(
    `${API_BASE_URL}/platform/network`,
    "POST",
    {
      name: requestBody?.name,
      description: requestBody?.description,
      isPrivate: requestBody?.isPrivate,
      managerId: requestBody?.managerId,
      networkType: requestBody?.networkType,
      domain: requestBody?.domain,
      ondcDomain : requestBody?.ondcDomain,
      defaultSellerId:requestBody?.defaultSellerId,
      multiSeller:requestBody?.multiSeller

    },
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch NetworkDetails");
  }
}

export async function updateNetWorkStatus(
  netWorkId: number,
  sellerId: number,
  request?: Request
): Promise<ApiResponse<NetWorkDetails[]>> {
  const response = apiRequest<NetWorkDetails[]>(
    `${API_BASE_URL}/bc/mnetadmin/nseller/${sellerId}/toggle-status `,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch NetworkDetails");
  }
}

export async function updateNetWorkAgentStatus(
  netWorkId: number,
  userId: number,
  request?: Request
): Promise<ApiResponse<networkAgents[]>> {
  const response = apiRequest<networkAgents[]>(
    `${API_BASE_URL}/bc/mnetadmin/network/${netWorkId}/user/${userId}/toggle-status`,
    "PUT",
    undefined,
    {},
    true,
    request
  );
  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch NetworkDetails");
  }
}
export async function updateNetWorkArea(
  nAreaId: number,
  request?: Request
): Promise<ApiResponse<NetWorkDetails[]>> {
  const response = apiRequest<NetWorkDetails[]>(
    `${API_BASE_URL}/bc/mnetadmin/narea/${nAreaId}/toggle-status `,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch NetworkDetails");
  }
}

export async function updateBannerStatus(
  bannerId: number,
  request?: Request
): Promise<ApiResponse<NetworkBanner>> {
  const response = apiRequest<NetworkBanner>(
    `${API_BASE_URL}/bc/mnetadmin/networkbanner/${bannerId} `,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch networkBanner");
  }
}
export async function deleteBanner(
  networkId:number,
  bannerId: number,
  request?: Request
): Promise<ApiResponse<NetworkBanner>> {
  const response = apiRequest<NetworkBanner>(
    `${API_BASE_URL}/bc/mnetadmin/${networkId}/nbanner/${bannerId} `,
    "PUT",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch networkBanner");
  }
}
export async function updateBannerSeq(
  seqId: number,
  nBannerData: NetworkBanner,
  request?: Request
): Promise<ApiResponse<NetworkBanner>> {
  const response = apiRequest<NetworkBanner>(
    `${API_BASE_URL}/bc/mnetadmin/networkbanners `,
    "PUT",
    [{
      id: nBannerData.id,
      networkId: nBannerData.networkId,
      bannerUrl: nBannerData.bannerUrl,
      sequenceId: seqId,
      target: nBannerData.target,
    }],
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch networkBanner");
  }
}
export async function updateBannerUrl(
  bannerUrl: string,
  nBannerData: NetworkBanner,
  request?: Request
): Promise<ApiResponse<NetworkBanner>> {
  const response = apiRequest<NetworkBanner>(
    `${API_BASE_URL}/bc/mnetadmin/networkbanners `,
    "PUT",
    [{
      id: nBannerData.id,
      networkId: nBannerData.networkId,
      bannerUrl: bannerUrl,
      sequenceId: nBannerData.sequenceId,
      target: nBannerData.target,
    }],
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch networkBanner");
  }
}

export async function createBanner(
  networkId: number,
  bannerUrl:string,
  sequenceId: number,
  request?: Request
): Promise<ApiResponse<NetworkBanner>> {
  const response = apiRequest<NetworkBanner>(
    `${API_BASE_URL}/bc/mnetadmin/${networkId}/create_nbanner `,
    "POST",
    {
      bannerUrl: bannerUrl,
      sequenceId: sequenceId,
    },
    {},
    true,
    request
  );
  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch networkBanner");
  }
}
export async function createNetworkSeller(
  netWorkId: number,
  sellerId: number,
  request?: Request
): Promise<ApiResponse<NetWorkDetails[]>> {
  const response = apiRequest<NetWorkDetails[]>(
    `${API_BASE_URL}/bc/mnetadmin/networkseller`,
    "POST",
    { networkId: netWorkId, sellerId: sellerId },
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch NetworkDetails");
  }
}

export async function getNetWorkAreas(
  netWorkId: number,
  access_token: string | null
): Promise<ApiResponse<NetWorkAreaDetails[]>> {
  return await apiRequest<NetWorkAreaDetails[]>(
    `${API_BASE_URL}/bc/mnetmanager/network/${netWorkId}/areas`,
    "GET",
    undefined,
    {
      Authorization: `Bearer ${access_token}`,
    },
    true
  );
}

export async function getNetworkTheme(
  request?: Request,
): Promise<ApiResponse<NetworkTheme>> {
 

  const response = await apiRequest<NetworkTheme>(
    `${API_BASE_URL}/bc/seller/theme`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch network config");
  }
}
