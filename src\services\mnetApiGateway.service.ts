// import { logger } from '@utils/logger.utils';
// import { config } from '@/utils/config.utils';
import { IServiceResponse } from '@/interfaces/service.interface.js';
import axios, { AxiosInstance } from 'axios';

import logger from '@/utils/express-logger.js';
import { CreateSupportTicketRequest, MasterItemDto } from '@/types/mnet/request.types.js';
import { isTokenExpired } from '@/utils/jwt.js';
import { CreateSupportTicketResponse, FreeItemResponse, GetUserDataResponse, ResponseDto } from '@/types/mnet/response.types.js';




class MnetApiGatewayService {

  private CORE_API_AUTH = process.env.CORE_API_AUTH;
  private API_BASE_URL = process.env.API_BASE_URL;
  private axiosInstance: AxiosInstance;
  private token: string = '';

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: this.API_BASE_URL,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${this.CORE_API_AUTH}`,
      },
    });

    // Add request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        logger.info(`Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
        logger.info(`Request Headers: ${JSON.stringify(config.headers)}`);
        logger.info(`Request Data: ${JSON.stringify(config.data)}`);
        return config;
      },
      (error) => {
        logger.error(`Request Error: ${error.message}`);
        return Promise.reject(error);
      }
    );

    // Add response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        logger.info(`Response: ${response.status} ${response.statusText}`);
        logger.info(`Response URL: ${response.config.baseURL}${response.config.url}`);
        logger.info(`Response Headers: ${JSON.stringify(response.headers)}`);
        logger.info(`Response Data: ${JSON.stringify(response.data)}`);
        return response;
      },
      (error) => {
        if (error.response) {
          logger.error(`Response Error: ${error.response.status} ${error.response.statusText}`);
          logger.error(`Response URL: ${error.response.config.url}`);
          logger.error(`Response Headers: ${JSON.stringify(error.response.headers)}`);
          logger.error(`Response Data: ${JSON.stringify(error.response.data)}`);
        } else if (error.request) {
          logger.error('No response received from API.');
        } else {
          logger.error(`Axios Error: ${error.message}`);
        }
        return Promise.reject(error);
      }
    );

  }

  async getAccessTokenFromRefreshToken(): Promise<string | null> {
    const url = `/oauth/access_token`;

    try {
      logger.info(`Getting Refresh token: ${JSON.stringify({})}`);
      const response = await this.axiosInstance.post<{ access_token: string }>(url, { grant_type: "refresh_token", refresh_token: "eyJhbGciOiJIUzI1NiJ9.NjllZGQ3YWYtZmI4MC00MzVhLThiZTctOTVjZGYzMmYxMzY5.plObErFzsn4RXI-Bsyd7LBqOPp9CUS-CX0dv_Asv6vY" });
      logger.info(`Getting Refresh token Response:  ${JSON.stringify(response.data)}`);
      return response.data.access_token;

    } catch (error) {
      return null;
    }

  }

  async getAccessToken(bMobile: string, cMobile: string): Promise<IServiceResponse<any>> {
    const url = `/wab/${bMobile}/c/${cMobile}/buyer/access_token`
    try {
      const response = await this.axiosInstance.get(url, {});
      return response.data;

    } catch (error) {
      return this.handleError(error);
    }
  }

  async addMasterItem(item: MasterItemDto): Promise<IServiceResponse<MasterItemDto>> {
    const url = `/platform/master_item`;

    if (!this.token || isTokenExpired(this.token)) {
      const newToken = await this.getAccessTokenFromRefreshToken();
      if (newToken) {
        this.token = newToken;
        this.axiosInstance.defaults.headers['Authorization'] = `Bearer ${this.token}`;
      } else {
        throw new Error('Failed to refresh token');
      }
    }

    try {
      logger.info(`Adding Master Item Request: ${JSON.stringify(item)}`);
      const response = await this.axiosInstance.post<MasterItemDto>(url, item);
      logger.info(`Adding Master Item Response:  + ${JSON.stringify(response.data)}`);
      return { ok: true, data: response.data };

    } catch (error) {
      return this.handleError(error);
    }

  }

  async createSupportTicket(ticket: CreateSupportTicketRequest, bMobile: string, cMobile: string): Promise<IServiceResponse<CreateSupportTicketResponse>> {
    const url = `/wab/${bMobile}/c/${cMobile}/create_ticket`;

    try {
      logger.info(`Creating Support Ticket Request: ${JSON.stringify(ticket)}`);
      const response = await this.axiosInstance.post<CreateSupportTicketResponse>(url, ticket);
      logger.info(`Creating Support Ticket Response:  + ${JSON.stringify(response.data)}`);
      return { ok: true, data: response.data };

    } catch (error) {
      return this.handleError(error);
    }
  }

  async getFreeItem(bMobile: string, cMobile: string): Promise<IServiceResponse<FreeItemResponse>> {
    const url = `/wab/${bMobile}/c/${cMobile}/offline-free-item`;

    try {
      logger.info(`Getting Free Item Request for business: ${bMobile}, customer: ${cMobile}`);
      const response = await this.axiosInstance.get<ResponseDto<FreeItemResponse>>(url);
      logger.info(`Getting Free Item Response: ${JSON.stringify(response.data)}`);
      
      if (!response.data.success || !response.data.data) {
        return { 
          ok: false, 
          err: response.data.error?.message || 'Failed to get free item' 
        };
      }

      return { ok: true, data: response.data.data };

    } catch (error) {
      return this.handleError(error);
    }
  }

  async getUserInfo(bMobile: string, cMobile: string, waIncomingCustomerName?: string): Promise<IServiceResponse<GetUserDataResponse>> {
    let url = `/wab/${bMobile}/c/${cMobile}`;
    if(waIncomingCustomerName) {
      url += `?cname=${waIncomingCustomerName}`;
    }

    try {
      logger.info(`Getting User Info Request for business: ${bMobile}, customer: ${cMobile}`);
      const response = await this.axiosInstance.get<GetUserDataResponse>(url);
      logger.info(`Getting User Info Response: ${JSON.stringify(response.data)}`);
      return { ok: true, data: response.data };

    } catch (error) {
      return this.handleError(error);
    }
  }

  private handleError<T>(error: unknown): IServiceResponse<T> {
    if (axios.isAxiosError(error)) {
      if (error.response) {
        return { ok: false, err: `API Error: ${error.response.status} - ${JSON.stringify(error.response.data)}` };
      } else if (error.request) {
        return { ok: false, err: 'No response received from API.' };
      } else {
        return { ok: false, err: `Axios Error: ${error.message}` };
      }
    } else if (error instanceof Error) {
      return { ok: false, err: `Unexpected Error: ${error.message}` };
    } else {
      return { ok: false, err: 'An unknown error occurred.' };
    }
  }


}

export default MnetApiGatewayService;
