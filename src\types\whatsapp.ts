import { CampaignType, CustomerSegment, MessageCategory } from "@/database/entities/NotificationLog.js";

//File:  types/whatsapp.ts
export interface WhatsAppMessageData {
    phoneNo: string;
    templateName: string;
    templateValues: string[];
    lang?: string;
    phoneNumberId?: string;
    accessToken?:string;
    businessId?: string; // Not in use, will be used for notification logs
}

// WhatsApp Message Types
export type WhatsAppLanguage = {
    code: string;
};

// Parameter Types
export type TextParameter = {
    type: 'text';
    paramter_name?: string;
    text: string;
};

export type CurrencyParameter = {
    type: 'currency';
    currency: {
        fallback_value: string;
        code: string;
        amount_1000: number;
    };
};

export type DateTimeParameter = {
    type: 'date_time';
    date_time: {
        fallback_value: string;
    };
};

export type ImageParameter = {
    type: 'image';
    image: {
        link: string;
    };
};

export type DocumentParameter = {
    type: 'document';
    document: {
        link: string;
        filename?: string;
    };
};

export type VideoParameter = {
    type: 'video';
    video: {
        link: string;
    };
};

export type PayloadParameter = {
    type: 'payload';
    payload: string;
};

export type ComponentParameter = 
    | TextParameter 
    | CurrencyParameter 
    | DateTimeParameter 
    | ImageParameter 
    | DocumentParameter 
    | VideoParameter 
    | PayloadParameter;

// Component Types
export type HeaderComponent = {
    type: 'header';
    parameters: (ImageParameter | DocumentParameter | VideoParameter | TextParameter)[];
};

export type BodyComponent = {
    type: 'body';
    parameters: ComponentParameter[];
};

export type ButtonComponent = {
    type: 'button';
    sub_type: 'quick_reply' | 'url';
    index: string;
    parameters: PayloadParameter[];
};

export type FooterComponent = {
    type: 'footer';
    parameters: TextParameter[];
};

export type TemplateComponent = HeaderComponent | BodyComponent | ButtonComponent | FooterComponent;

// Template Message Types
export type WhatsAppTemplate = {
    name: string;
    language: WhatsAppLanguage;
    components?: TemplateComponent[];
};

export type WhatsAppTemplateMessageData = {
    targetPhoneNumber: string;
    wabPhoneNumberId?: string;
    accessToken?: string;
    template: WhatsAppTemplate;
    businessId?: string; // Not in use, will be used for notification logs  
};

// Template CTA Types
export type WaTemplateCTAType = 'quick_reply' | 'url';

export interface WaTemplateCta {
    id: number;
    label: string;
    value: string;
    type: WaTemplateCTAType;
}

export interface Template {
    id: number;
    templateId: string;
    name: string;
    header?: string;
    content: string;
    preview?: string;
    type: string;
    ctas: WaTemplateCta[];
    phone: string;
    bodyVariables?: string[];
    headerVariables?: string[];
    footerVariables?: string[];
    buttonsVariables?: { index: number; value: string; }[];
    lastUsed: string;
}

export interface ComponentVariables {
    header?: { [key: string]: string };
    body?: { [key: string]: string };
    button?: { [key: string]: string };
    footer?: { [key: string]: string };
}

export interface SendTemplateRequest {
    templateId: string;
    variables: ComponentVariables;
    targetPhoneNumber: string;
    wabPhoneNumberId?: string;
    accessToken?: string;
}

export interface TemplateCampaignRequest extends SendTemplateRequest {
    campaignId: string;
    campaignName: string;
    campaignType?: CampaignType;
    messageCategory?: MessageCategory;
    customerSegment?: CustomerSegment;
    tags?: string[];
}

export interface SendTemplateResponse {
    success: boolean;
    message: string;
    details?: any;
    error?: any;
}

export interface MetaWhatsAppContact {
    input: string;
    wa_id: string;
}

export interface MetaWhatsAppMessage {
    id: string;
    message_status: string;
}

export interface MetaWhatsAppResponseDetails {
    messaging_product: string;
    contacts: MetaWhatsAppContact[];
    messages: MetaWhatsAppMessage[];
}

export interface MetaWhatsAppResponse {
    success: boolean;
    message: string;
    details: MetaWhatsAppResponseDetails;
}

export interface WhatsAppConnectionData {
    access: {
        access_token: string;
        token_type: string;
        expires_in: number;
        test_data?: boolean;
        inserted_at?: string;
    };
    user: {
        userId: number;
        userName: string;
    };
    mNetConnectedPhoneNumberId?: string;
    mNetConnectedPhoneNumber?: string;
    mNetConsolePhoneNumber?: string;
    wabaId?: string;
    businessName: string;
    sellerId: number;
    updatedAt: string;
    isTestData?: boolean;
    qrCodeData?: {
        code: string;
        prefilled_message: string;
        deep_link_url: string;
        qr_image_url: string;
        created_at: string;
    };
    ctwaToken?: string;
}
