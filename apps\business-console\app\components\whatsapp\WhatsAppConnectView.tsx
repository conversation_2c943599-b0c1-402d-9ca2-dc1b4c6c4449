import {useState, useEffect} from 'react';
import {Link, useFetcher} from "@remix-run/react";
import {Card, CardHeader, CardTitle, CardDescription, CardContent} from '@components/ui/card';
import type {WhatsAppConnectionData, WhatsAppBusinessAccount, PhoneNumber} from "~/types/whatsapp";
import {PhoneNumberSelector} from "@components/whatsapp/PhoneNumberSelector";

interface WhatsAppConnectViewProps {
    FACEBOOK_APP_ID: string;
    connectionState: WhatsAppConnectionData | null;
}

export function WhatsAppConnectView({FACEBOOK_APP_ID, connectionState}: WhatsAppConnectViewProps) {
    const fetcher = useFetcher();
    const [businessAccounts, setBusinessAccounts] = useState<WhatsAppBusinessAccount[]>([]);
    const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);
    const [selectedBusinessId, setSelectedBusinessId] = useState<string | null>(null);
    const [hasFetchedBusinesses, setHasFetchedBusinesses] = useState(false);
    const [isSubscribed, setIsSubscribed] = useState(false);
    const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);
    const [currentView, setCurrentView] = useState<'login' | 'businesses' | 'phones' | 'registered'>('login');
    const CONFIG_ID = '***************';
    const REQUIRED_APP_ID = '***************';

    // Set initial view based on connection state
    useEffect(() => {
        if (!connectionState?.access) {
            setCurrentView('login');
        } else if (connectionState.mNetConnectedPhoneNumberId) {
            setCurrentView('registered');
        } else if (!hasFetchedBusinesses) {
            setCurrentView('businesses');
        }
    }, [connectionState, hasFetchedBusinesses]);

    useEffect(() => {
        if (
            currentView === 'registered' &&
            connectionState?.wabaId &&
            !hasCheckedSubscription &&
            fetcher.state === 'idle'
        ) {
            const formData = new FormData();
            formData.append('actionType', 'check-subscription');
            formData.append('businessId', connectionState.wabaId);
            fetcher.submit(formData, {method: 'post'});
            setHasCheckedSubscription(true);
        }
    }, [currentView, connectionState?.wabaId, hasCheckedSubscription, fetcher.state]);


    useEffect(() => {
        if (currentView !== 'registered') {
            setHasCheckedSubscription(false);
        }
    }, [currentView]);
    // Fetch business accounts
    useEffect(() => {
        if (
            connectionState?.access &&
            !hasFetchedBusinesses &&
            fetcher.state === 'idle' &&
            currentView === 'businesses'
        ) {
            setHasFetchedBusinesses(true);
            const formData = new FormData();
            formData.append('actionType', 'fetch-businesses');
            fetcher.submit(formData, {method: 'post'});
        }
    }, [connectionState?.access, hasFetchedBusinesses, fetcher.state, currentView]);

    // Handle fetcher responses
    useEffect(() => {
        if (fetcher.data?.success) {
            if ('businessAccounts' in fetcher.data) {
                setBusinessAccounts(fetcher.data.businessAccounts);
            } else if ('phoneNumbers' in fetcher.data) {
                setPhoneNumbers(fetcher.data.phoneNumbers);
            } else if ('subscribedApps' in fetcher.data) {
                setIsSubscribed(fetcher.data.subscribedApps.some(app =>
                    app.whatsapp_business_api_data?.id === REQUIRED_APP_ID
                ));
            } else if ('actionType' in fetcher.data) {
                switch (fetcher.data.actionType) {
                    case 'connect-phone':
                        setCurrentView('registered');
                        break;
                    case 'delink':
                        setCurrentView('login');
                        break;
                    case 'subscribe':
                    case 'unsubscribe':
                        // Refresh subscription status
                        const formData = new FormData();
                        formData.append('actionType', 'check-subscription');
                        formData.append('businessId', connectionState!.wabaId!);
                        fetcher.submit(formData, {method: 'post'});
                        break;
                }
            }
        }
    }, [fetcher.data]);

    const handleSubscriptionToggle = () => {
        if (!connectionState?.wabaId) {
            console.error('No WABA ID found');
            return;
        }

        const formData = new FormData();
        formData.append('actionType', isSubscribed ? 'unsubscribe' : 'subscribe');
        formData.append('businessId', connectionState.wabaId);
        setHasCheckedSubscription(false);
        fetcher.submit(formData, {method: 'post'});
    };

    // Handle business selection
    const handleBusinessSelect = async (businessId: string) => {
        setSelectedBusinessId(businessId);
        setCurrentView('phones');
        const formData = new FormData();
        formData.append('actionType', 'fetch-phone-numbers');
        formData.append('businessId', businessId);
        fetcher.submit(formData, {method: 'post'});
    };

    // Facebook login setup
    useEffect(() => {
        window.fbAsyncInit = function () {
            window.FB.init({
                appId: FACEBOOK_APP_ID,
                cookie: true,
                xfbml: true,
                version: 'v21.0'
            });
        };

        const script = document.createElement('script');
        script.src = "https://connect.facebook.net/en_US/sdk.js";
        script.async = true;
        script.defer = true;
        document.body.appendChild(script);

        return () => {
            document.body.removeChild(script);
        };
    }, [FACEBOOK_APP_ID]);

    const handleFacebookLogin = () => {
        window.FB.login((response) => {
            if (response.status === 'connected' && response.authResponse?.code) {
                const formData = new FormData();
                formData.append('code', response.authResponse.code);
                formData.append('actionType', 'exchange-token');
                fetcher.submit(formData, {method: 'post'});
            }
        }, {
            config_id: CONFIG_ID,
            response_type: 'code',
            override_default_response_type: true
        });
    };

    return (
        <div className="space-y-6">
            {fetcher.state !== 'idle' && (
                <Card className="w-full max-w-3xl mx-auto">
                    <CardHeader>
                        <CardTitle>Loading...</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-center p-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    </CardContent>
                </Card>
            )}

            {fetcher.state === 'idle' && currentView === 'login' && (
                <Card className="w-full max-w-3xl mx-auto">
                    <CardHeader>
                        <CardTitle>Connect WhatsApp Business</CardTitle>
                        <CardDescription>Connect your WhatsApp Business account to continue</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <button
                            onClick={handleFacebookLogin}
                            className="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700"
                        >
                            Continue with Facebook
                        </button>
                    </CardContent>
                </Card>
            )}

            {fetcher.state === 'idle' && currentView === 'businesses' && (
                <Card className="w-full max-w-3xl mx-auto">
                    <CardHeader>
                        <CardTitle>Select WhatsApp Business Account</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div>
                            {businessAccounts.map(account => (
                                <div
                                    key={account.id}
                                    className="mb-4 p-4 border rounded cursor-pointer hover:bg-gray-50"
                                    onClick={() => handleBusinessSelect(account.id)}
                                >
                                    <div className="font-medium">{account.name}</div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {fetcher.state === 'idle' && currentView === 'phones' && (
                <div>
                    {phoneNumbers.length > 0 && (
                        <PhoneNumberSelector
                            numbers={phoneNumbers}
                            selectedId={connectionState?.mNetConnectedPhoneNumberId}
                            onSubmit={(phoneId, phoneNumber) => {
                                const formData = new FormData();
                                formData.append('actionType', 'connect-phone');
                                formData.append('phoneId', phoneId);
                                formData.append('phoneNumber', phoneNumber);
                                formData.append('wabaId', selectedBusinessId!);
                                fetcher.submit(formData, { method: 'post' });
                            }}
                        />
                    )}
                </div>
            )}

            {fetcher.state === 'idle' && currentView === 'registered' && (
                <Card className="w-full max-w-3xl mx-auto">
                    <CardHeader>
                        <CardTitle>WhatsApp Business Registered</CardTitle>
                        <CardDescription>Your WhatsApp Business account is ready to use</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div>
                                <h3 className="font-medium">Connected Phone Number</h3>
                                <p>{connectionState?.mNetConnectedPhoneNumber}</p>
                            </div>
                            <div>
                                <h3 className="font-medium">Subscription Status</h3>
                                <p className={isSubscribed ? "text-green-600" : "text-yellow-600"}>
                                    {isSubscribed ? "Subscribed" : "Not Subscribed"}
                                </p>
                            </div>
                            <div className="space-y-2">
                                <button
                                    onClick={handleSubscriptionToggle}
                                    className={`w-full px-4 py-2 rounded-md font-medium ${
                                        isSubscribed
                                            ? "bg-red-500 hover:bg-red-600 text-black"
                                            : "bg-emerald-500 hover:bg-emerald-600 text-black"
                                    }`}
                                >
                                    {isSubscribed ? "Unsubscribe" : "Subscribe"}
                                </button>

                                <Link
                                    to="/home/<USER>"
                                    className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 inline-block text-center"
                                >
                                    Manage Templates
                                </Link>

                                <fetcher.Form method="post">
                                    <input type="hidden" name="actionType" value="delink"/>
                                    <button
                                        type="submit"
                                        className="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
                                        onClick={(e) => {
                                            if (!confirm('Are you sure you want to delink your WhatsApp Business account?')) {
                                                e.preventDefault();
                                            }
                                        }}
                                    >
                                        Delink Account
                                    </button>
                                </fetcher.Form>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );


}
