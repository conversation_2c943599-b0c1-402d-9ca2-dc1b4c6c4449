// src/index.ts
import dotenv from 'dotenv';
import express from 'express';
import { createRequestHandler } from '@remix-run/express';
import { broadcastDevReady, type ServerBuild } from '@remix-run/node';
import { authenticateToken } from '@middleware/auth.js';
import { configureRoutes } from '@routes/index.js';
import { validateEnvironment } from '@utils/environment.js';
import path from 'path';
import { fileURLToPath } from 'url';
dotenv.config();
validateEnvironment();

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const BUILD_DIR = path.join(__dirname, "../apps/business-console/build");
const PROD = process.env.NODE_ENV === "production";

const app = express();
const port = process.env.PORT || 8080;

// Body parser middleware
app.use(express.json());

// Add health check endpoint for Elastic Beanstalk
app.get('/health', (req, res) => {
    res.status(200).send('OK');
});

// Serve static files from Remix build
app.use(express.static(path.join(BUILD_DIR, "client"), {
    maxAge: PROD ? "1y" : "0",
}));

// Function to get the Remix build
const getRemixBuild = async (): Promise<ServerBuild> => {
    if (PROD) {
        return await import(BUILD_DIR + '/server/index.js');
    }
    // In development, always load the latest build
    const build = await import(BUILD_DIR + '/server/index.js?update=' + Date.now());
    if (!PROD) {
        await broadcastDevReady(build);
    }
    return build;
};

// Handle API routes specifically
app.use('/api', (req, res, next) => {
    console.log('API route hit:', req.path);
    next();
});

// Configure API routes
configureRoutes(app, authenticateToken);

// Handle all other routes with Remix - this should come last
app.all(
    "*",
    (req, res, next) => {
        // Skip this middleware if path starts with /api
        if (req.path.startsWith('/api')) {
            return next('route');
        }
        return createRequestHandler({
            build: getRemixBuild,
            mode: PROD ? "production" : "development",
        })(req, res, next);
    }
);

app.listen(port, () => {
    console.log(`Server listening on port ${port}`);
});
