// File: app/types/whatsapp.ts
export interface WhatsAppConnectionData {
    access: {
        access_token: string;
        token_type: string;
        expires_in: number;
        test_data?: boolean;
        inserted_at?: string;
    };
    user: {
        userId: number;
        userName: string;
    };
    mNetConnectedPhoneNumberId?: string;
    mNetConnectedPhoneNumber?: string;
    mNetConsolePhoneNumber?: string;
    wabaId?: string;
    businessName: string;
    sellerId: number;
    updatedAt: string;
    isTestData?: boolean;
    qrCodeData?: {
        code: string;
        prefilled_message: string;
        deep_link_url: string;
        qr_image_url: string;
        created_at: string;
    };
}

export interface WhatsAppSubscribedAppResponse {
    data: {
        whatsapp_business_api_data: {
            name: string;
            id: string;
        }
    }
}

export interface WhatsAppSubscribedApp {
    name: string;
    id: string;
}


export interface FacebookResponse {
    status: string;
    authResponse?: {
        code: string;
        [key: string]: any;
    };
}

export interface WhatsAppBusinessAccount {
    id: string;
    name: string;
}

export interface WhatsAppPhoneNumber {
    id: string;
    display_phone_number: string;
    verified_name: string;
    status: 'PENDING' | 'ACTIVE' | 'DELETED';
    quality_rating: 'GREEN' | 'YELLOW' | 'RED' | 'UNKNOWN';
    search_visibility: 'VISIBLE' | 'NON_VISIBLE';
    platform_type: string;
    code_verification_status: 'VERIFIED' | 'EXPIRED' | 'NOT_VERIFIED';
    country_dial_code?: string;
}


export interface DebugTokenResponse {
    data: {
        app_id: string;
        granular_scopes: Array<{
            scope: string;
            target_ids?: string[];
        }>;
        is_valid: boolean;
    };
}

export interface PhoneNumber {
    id: string;
    verified_name: string;
    code_verification_status: string;
    display_phone_number: string;
    quality_rating: string;
    platform_type: string;
}

export interface WhatsAppTemplate {
    name: string;
    parameter_format: string;
    components: {
        type: string;
        format?: string;
        text: string;
    }[];
    language: string;
    status: string;
    category: string;
    id: string;
}

export interface TemplatesResponse {
    data: WhatsAppTemplate[];
    paging: {
        cursors: {
            before: string;
            after: string;
        };
    };
}

export interface WhatsAppBusinessProfile {
    about?: string;
    address?: string;
    description?: string;
    email?: string;
    messaging_product: string;
    vertical?: string;
    websites?: string[];
    profile_picture_url?: string;
}

export interface BusinessProfileResponse {
    data: WhatsAppBusinessProfile[];
}


