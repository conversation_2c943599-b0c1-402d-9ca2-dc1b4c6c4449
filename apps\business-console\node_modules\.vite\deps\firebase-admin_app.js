import {
  require_app
} from "./chunk-GCPDFCKO.js";
import "./chunk-JUECKKJD.js";
import "./chunk-6DOPJS7E.js";
import "./chunk-FG6CHLFO.js";
import "./chunk-UJS6U7L7.js";
import "./chunk-JPJABU35.js";
import "./chunk-SJGIY2NI.js";
import {
  __toESM
} from "./chunk-N5SXXOWC.js";

// node_modules/firebase-admin/lib/esm/app/index.js
var import_app = __toESM(require_app());
var AppErrorCodes = import_app.default.AppErrorCodes;
var FirebaseAppError = import_app.default.FirebaseAppError;
var SDK_VERSION = import_app.default.SDK_VERSION;
var applicationDefault = import_app.default.applicationDefault;
var cert = import_app.default.cert;
var deleteApp = import_app.default.deleteApp;
var getApp = import_app.default.getApp;
var getApps = import_app.default.getApps;
var initializeApp = import_app.default.initializeApp;
var refreshToken = import_app.default.refreshToken;
export {
  AppErrorCodes,
  FirebaseAppError,
  SDK_VERSION,
  applicationDefault,
  cert,
  deleteApp,
  getApp,
  getApps,
  initializeApp,
  refreshToken
};
//# sourceMappingURL=firebase-admin_app.js.map
