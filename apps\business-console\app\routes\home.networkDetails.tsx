import { useState, useCallback, useEffect, useRef } from 'react'
import { json, useF<PERSON>cher, use<PERSON>oaderD<PERSON>, useNavigate } from "@remix-run/react";
import { useSubmit } from "@remix-run/react";
import { addNetworkLocalities, getNetworkAreas, getNetworkBanners, getNetworkBuyers, getNetworkConfig, getNeworkAgents, getsmDistrictsAndStates } from "~/services/businessConsoleService";
import { NetworkAreas, NetworkBanner, NetworkBuyer, NetworkConfig } from "~/types/api/businessConsoleService/Network";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@components/ui/select"
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Edit, Pencil, Save, Trash2, X } from "lucide-react";
import NetworkAreaCard from "~/components/ui/customNetworkAreaCard";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "~/components/ui/tabs";
import { GoogleMap, InfoWindow, LoadScript, Marker, Polygon } from '@react-google-maps/api';
import { decodePolygon } from '~/utils/polyline-utils';
import { MasterLocalities, networkAgents, StateAndDistricts } from '~/types/api/businessConsoleService/SellerManagement';
import { Checkbox } from '~/components/ui/checkbox';
import { createAgent, getSelectedNetWorkAgent, getSellerList, updateAttributes } from '~/services/masterItemCategories';
import { Button } from '~/components/ui/button';
import CreateAgent, { RoleOption } from '~/components/ui/createAgent';
import { ResponsiveTable } from '~/components/ui/responsiveTable';
import { createBanner, createNetworkSeller, deleteBanner, getNetWorkSeller, updateBannerSeq, updateBannerStatus, updateBannerUrl, updateNetWorkAgentStatus, updateNetWorkArea, updateNetWorkStatus } from '~/services/netWorks';
import { NetWorkDetails } from '~/types/api/businessConsoleService/netWorkinfo';
import { NetWorkModal } from '~/components/ui/netWorkModal';
import { Switch } from '~/components/ui/switch';
import { useToast } from '~/components/ui/ToastProvider';
import { Seller } from '~/types/api/businessConsoleService/MasterItemCategory';
import NetWorkConfig from './netWorkConfig';
import { Input } from '~/components/ui/input';
import CreateNetworkBannerModal from '~/components/common/NetworkBanneModal';
import SpinnerLoader from '~/components/loader/SpinnerLoader';
import { useDebounce } from '~/hooks/useDebounce';
import ResponsivePagination from '~/components/ui/responsivePagination';
import EditBuyerModal from '~/components/common/EditBuyerModal';
import { console } from 'inspector';
export interface newLocal {
  id: number | null;
  networkId: number | null;
  agentUserId: string | null;
  localities: MasterLocalities | null;
}
interface LoaderData {
  googleMapsApiKey: string
  networkId: number,
  networkName: string,
  activeTab: string,
  networkAreas?: NetworkAreas[],
  networkConfig?: NetworkConfig[],
  networkBanners?: NetworkBanner[],
  networkBuyers?: NetworkBuyer[],
  statesAndDistricts?: StateAndDistricts[],
  networkAgents?: networkAgents[],
  url: string,
  userId: number,
  roles: RoleOption[],
  networkSeller: NetWorkDetails[],
  sellerList: Seller[],
  permissions: string[],
  pageSize: number;
  currentPage: number;

}

export interface ActionData {
  sucess?: boolean,
  error?: string
}

const BANGALORE_CENTER = {
  lat: 12.9716,
  lng: 77.5946
}

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '100%'
}
const getPolygonColor = (index: number) => {
  const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#f97316']
  return colors[index % colors.length]
}
export const loader = withAuth(async ({ user, request }) => {
  const permissions = user?.userDetails?.roles
  const isSales = permissions?.includes('FmSalesManager')

  const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY || ''
  const url = new URL(request.url);
  const networkId = Number(url.searchParams.get("networkId"));
  const networkName = (url.searchParams.get("networkName"));
  const pageSize = parseInt(url.searchParams.get("pageSize") || "20");
  const currentPage = parseInt(url.searchParams.get("currentPage") || "0");
  const matchBy = url.searchParams.get("matchBy") || "";
  type ActiveTab = "NetworkConfig" | "NetworkAreas" | "NetworkAgents" | "NetworkSeller" | "NetworkBanners" | "NetworkBuyers";

  const activeTab: ActiveTab = url.searchParams.get("activeTab") as ActiveTab || (!isSales ? "NetworkConfig" : "NetworkAgents");
  let networkAreas: NetworkAreas[] | [] = [];
  let statesAndDistricts: StateAndDistricts[] | [] = [];
  let networkConfig: NetworkConfig[] | [] = [];
  let networkBanners: NetworkBanner[] | [] = [];
  let networkBuyers: NetworkBuyer[] | [] = [];

  let networkAgents: networkAgents[] | [] = [];
  let networkSeller: NetWorkDetails[] | [] = []
  let sellerList: Seller[] | [] = []
  let roles: RoleOption[] | [] = [];
  let userId = user.userId;

  let response;
  try {
    switch (activeTab) {
      case "NetworkAreas":
        const [areasResponse, districtsAndStatesResponse, networkAgentsResponse] = await Promise.all([getNetworkAreas(networkId, request),
        getsmDistrictsAndStates(user.userId, request), getNeworkAgents(networkId, request)]);
        networkAreas = areasResponse?.data || [];
        statesAndDistricts = districtsAndStatesResponse?.data || [];
        networkAgents = networkAgentsResponse?.data || [];
        break;
      case "NetworkConfig":
        response = await getNetworkConfig("networkDomain", networkId, request);
        networkConfig = response?.data || [];
        console.log(networkConfig, "66666666666666", response.data, "111111")
        break;
      case "NetworkAgents":
        response = await getSelectedNetWorkAgent(networkId, request);
        networkAgents = response?.data || [];
        break;
      case "NetworkBanners":
        response = await getNetworkBanners(networkId, request);
        networkBanners = response?.data || [];
        console.log(networkBanners, "66666666666666", response.data, "111111")
        break;
      case "NetworkSeller":
        console.log(networkId, "999999999999999")
        const [networkSellerResponse, masterSellerResponse] = await Promise.all([getNetWorkSeller(networkId, request), getSellerList(request)])
        //response = await getNetWorkSeller(networkId, request);        
        networkSeller = networkSellerResponse?.data || [];
        sellerList = masterSellerResponse?.data || [];
        break;
      case "NetworkBuyers":
        const [buyersResponse, agentsResponse] = await Promise.all([getNetworkBuyers(networkId, pageSize, currentPage, matchBy, request), getNeworkAgents(networkId, request)]);
        networkBuyers = buyersResponse?.data || [];
        networkAgents = agentsResponse?.data || [];
        break;

    }
    return withResponse({
      googleMapsApiKey,
      networkId,
      networkName,
      activeTab,
      networkAreas,
      statesAndDistricts,
      networkConfig,
      networkBanners,
      networkBuyers,
      networkAgents,
      url,
      userId,
      roles,
      networkSeller,
      sellerList,
      permissions,
      pageSize: pageSize,
      currentPage: currentPage,

    }, response?.headers);


  } catch (error) {
    console.log("loader failed");
    console.error("Error in loader:", error);
    // Return a JSON-based error shape
    return [];
  }
});

export const action = async ({ request }: { request: Request }) => {
  const formData = await request.formData();
  const intent = formData.get("_intent");
  const netWorkId = formData.get("netWorkId") as unknown as number;
  const sellerId = formData.get("sellerId") as unknown as number;
  const actionType = formData.get("actionType");
  const nAreaId = formData.get("nAreaId") as unknown as number;
  const attribute = formData.get("attribute") as string;
  const updateValue = formData.get("value");
  const type = formData.get("updateType") as string;
  const domainId = formData.get("domainId") as unknown as number;
  const BannerId = Number(formData.get("bannerId"));

  if (actionType === "updateNetWorkAreaEdit") {
    const agentUserId = formData.get("agentUserId") as unknown as number;
    const networkId = formData.get("editNetworkId") as unknown as number;
    const area = formData.get("masterAreaId") as unknown as number;
    const areas: number[] = [area];
    try {
      console.log(`📡 Sending API request for agentUserId: ${agentUserId}`);
      const apiResponse = await addNetworkLocalities(networkId, agentUserId, areas, request);
      console.log("✅ API Response:", apiResponse);
      return apiResponse
    } catch (error) {
      console.error(`❌ Error in API call for agentUserId ${agentUserId}:`, error);
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }
  if (actionType === "updateNetWorkAreaStatus") {
    try {
      const response = await updateNetWorkArea(nAreaId, request)
      return withResponse({
        NetWorkAreas: response.data,
        netWorkId: netWorkId,
        sellerId: sellerId,
        nAreaId: nAreaId
      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      else throw new Response("Something Went Wrong", { status: 500 });

    }
  }
  if (actionType === "updateNetWorkAreaStatus") {
    try {
      const response = await updateNetWorkArea(nAreaId, request)
      return withResponse({
        NetWorkAreas: response.data,
        netWorkId: netWorkId,
        sellerId: sellerId,
        nAreaId: nAreaId
      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      else throw new Response("Something Went Wrong", { status: 500 });

    }
  }
  if (actionType === "updateBanner") {

    console.log("deleteBanner.......")

    try {
      const response = await deleteBanner(netWorkId, BannerId, request)
      return withResponse({
        networkBanners: response.data,

      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Something Went Wrong", { status: 500 });

    }
  }
  if (actionType === "bannerSequence") {
    const bannerData = JSON.parse(formData.get("bannerData") as string || "{}");
    const seqId = Number(formData.get("seQuenceId"))

    try {
      const response = await updateBannerSeq(seqId, bannerData, request)
      return withResponse({
        networkBanners: response.data,

      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }

  if (actionType === "updateBannerUrl") {
    const bannerData = JSON.parse(formData.get("bannerData") as string || "{}");
    const bannerUrl = formData.get("bannerUrl") as string
    try {
      const response = await updateBannerUrl(bannerUrl, bannerData, request)
      return withResponse({
        networkBanners: response.data,

      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }
  if (actionType === "createNetworkBanner") {
    const networkId = Number(formData.get("networkId"));
    const sequenceId = Number(formData.get("sequenceId"));
    const bannerUrl = formData.get("bannerUrl") as string
    try {
      const response = await createBanner(networkId, bannerUrl, sequenceId, request)
      return withResponse({
        sucess: response.statusCode,
      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }



  const supportedAttributes = [
    "business_logo",
    "home_page_banner", "pwa_app_icon", "footer_app_icon", "multi_seller", "default_seller_id",
    "wab_enabled", "wab_mobile_number", "default_start_page", "domain", "wabDatasetId"
  ];
  if (supportedAttributes.includes(attribute)) {

    try {
      const updatedResponse = await updateAttributes(type, domainId, attribute, updateValue, request);


      console.log("Updating Network details:", { attribute, updateValue });

      return withResponse(
        {
          sellerConfig: updatedResponse.data,
          sellerId: sellerId,
        },
        updatedResponse.headers
      );
    }
    catch (error) {
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }

  if (actionType === "updateNetWorkStatus") {
    try {
      const response = await updateNetWorkStatus(netWorkId, sellerId, request)
      return withResponse({
        networkSeller: response.data,

        netWorkId: netWorkId,
        sellerId: sellerId
      }, response.headers);
    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Failed to NetWork Seller Status", { status: 500 });
    }

  }

  if (actionType === "updateAgentStatus") {
    const agentUserId = formData.get("agentUserId") as unknown as number

    try {
      const response = await updateNetWorkAgentStatus(netWorkId, agentUserId, request)

      return withResponse({
        networkAgents: response.data,
        netWorkId: netWorkId,
        sellerId: sellerId,
        agentUserId: agentUserId
      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Failed to NetWork Seller Status", { status: 500 });
    }

  }
  if (actionType === "updateNetWorkStatus") {
    try {
      const response = await updateNetWorkStatus(netWorkId, sellerId, request)


      return withResponse({
        networkSeller: response.data,

        netWorkId: netWorkId,
        sellerId: sellerId
      }, response.headers);

    }
    catch (error) {
      if (error instanceof Response && error.status === 404) {

        throw json({ error: "update NetWork Status page Not found" }, { status: 404 });
      }
      throw new Response("Failed to NetWork Seller Status", { status: 500 });
    }

  }
  if (intent === "createAgent") {
    const newAgent = {
      firstName: formData.get("firstName") as string,
      lastName: formData.get("lastName") as string,
      email: formData.get("email") as string,
      mobileNumber: formData.get("mobileNumber") as string,
      address: formData.get("address") as string,
      password: formData.get("password") as string,
      businessId: Number(formData.get("businessId")),
      roles: (formData.get("roles") as string)?.split(","),
    };
    try {
      await createAgent(Number(netWorkId), newAgent, request);
      return json({ success: true });
    } catch (error) {
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }

  if (intent === "createNetWork") {

    try {
      const response = await createNetworkSeller(netWorkId, sellerId, request);
      return withResponse({
        networkSeller: response.data,
        netWorkId: netWorkId

      }, response.headers);

    } catch (error) {
      throw new Response("Something Went Wrong", { status: 500 });
    }
  }
  if (request.method !== "POST") {
    return json({ error: "Method Not Allowed" }, { status: 405 });
  }

  if (actionType === "addLocalities") {
    try {
      console.log("📩 Received formData:", Object.fromEntries(formData));
      console.log("✅ Call `addNetworkLocalities`");
      const newLocalities = JSON.parse(formData.get("localities") as string || "[]");
      console.log(`newLocalities : `, newLocalities);
      if (!newLocalities || !Array.isArray(newLocalities) || newLocalities.length === 0) {
        console.error("❌ Invalid request payload");
        return json({ error: "Invalid request payload" }, { status: 400 });
      }

      const apiResponses = [];
      for (const locality of newLocalities) {
        const { agentUserId, networkId, areas } = locality;

        try {
          console.log(`📡 Sending API request for agentUserId: ${agentUserId}`);
          const apiResponse = await addNetworkLocalities(networkId, agentUserId, areas, request);
          console.log("✅ API Response:", apiResponse);
          apiResponses.push(apiResponse);
        } catch (error) {
          console.error(`❌ Error in API call for agentUserId ${agentUserId}:`, error);
          apiResponses.push({ error: `Failed for agentUserId ${agentUserId}` });
        }
      }
      return json({ success: true, data: apiResponses });
    }

    catch (error) {
      console.error("❌ Error processing submission:", error);
      return json({ error: "Internal Server Error" }, { status: 500 });
    }
  }
};
export default function NetworkDetailsPage() {
  const { googleMapsApiKey, networkId, networkName, activeTab, networkAreas, statesAndDistricts, networkConfig, networkBanners, networkBuyers, networkAgents, url, userId, roles, networkSeller, sellerList, permissions, pageSize, currentPage } = useLoaderData<LoaderData>()
  // If no networks, show a "No results" row
  const dataleng = networkAreas ? networkAreas.length : networkConfig;
  const fetchfor = useNavigate();
  const handleTabChange = (newTab: string) => {
    console.log(newTab, "333333333")

    fetchfor(`?networkId=${networkId}&networkName=${networkName}&activeTab=${newTab}`);
  };

  console.log(networkBanners, "09890989098989")
  const [visibleAreas, setVisibleAreas] = useState<Set<number>>(new Set())
  const [isLoading, setIsLoading] = useState(true); // Controls loader visibility
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const onLoad = useCallback((mapInstance: google.maps.Map) => {
    setMap(mapInstance)
    setMapLoaded(true)
  }, [])
  let stateList: string[] = [];
  statesAndDistricts?.forEach((area) => stateList.push(area.state));
  interface InfoWindowState {
    areaId: number | null;
    isShown: boolean;
    selectedAreaDetails: NetworkAreas | null;
  }
  interface pointerLocation {
    latitude: number | null;
    longitude: number | null;
  }
  const [latitude, setLatitude] = useState(0);
  const [longitude, setLongitude] = useState(0);
  const [showMarker, setShowMarker] = useState(false);
  const [isLocateShopClicked, setisLocateShopClicked] = useState(false);
  const [isAddLocalityClicked, setisAddLocalityClicked] = useState(false);
  const [localityEditMode, setlocalityEditMode] = useState(false);
  const [selectedState, setSelectedState] = useState('');
  const [selectedDistrict, setSelectedDistrict] = useState('');
  const [masterLocalities, setMasterLocalities] = useState<MasterLocalities[]>([]);
  const [pointerLocation, setPointerLocation] = useState<pointerLocation>({
    latitude: null,
    longitude: null
  });

  const [infoWindowShown, setInfoWindowShown] = useState<InfoWindowState>({
    areaId: null,
    isShown: false,
    selectedAreaDetails: null
  });
  const [newLocalities, setNewLocalities] = useState<MasterLocalities[]>([])
  const [newLocalities2, setNewLocalities2] = useState<newLocal[]>([])
  const handleFindLocation = useCallback((
    lat: number,
    long: number,
    showMarker: boolean
  ) => {
    if (lat && long) {
      setPointerLocation({ latitude: lat, longitude: long });
      setShowMarker(showMarker);
    }
    else {
      alert("Please enter valid numeric values for latitude and longitude.");
    }
  }, []);

  const salesPermissions = permissions.includes("FmSalesManager")


  const handleNewLocalities = useCallback((id: number, localityDetails: MasterLocalities, agentUserId?: number) => {
    setNewLocalities((prevState) => {
      if (prevState.some((abc) => abc.id === id)) {
        // Remove the locality if it exists
        return prevState.filter((abc) => abc.id !== id);
      } else {
        // Add the new locality
        return [...prevState, localityDetails];
      }
    });
  }, []);


  const resetNewLocalities = useCallback(() => {
    setNewLocalities([]);
    setNewLocalities2([]);
    setlocalityEditMode(false);
  }, []);

  const handleNewLocalities2 = useCallback(
    (id: number, localityDetails: MasterLocalities, networkId: number, agentUserId?: string) => {
      setNewLocalities2((prevState) => {
        const existingIndex = prevState.findIndex((loc) => loc.id === id);
        console.log("prevState.....", prevState);

        if (existingIndex !== -1) {
          // Update agentUserId instead of removing
          console.log("id found... updating agentUserId ");
          return prevState.map((loc) =>
            loc.id === id ? { ...loc, agentUserId: agentUserId ?? null } : loc
          );
        } else {
          // Add new locality
          console.log("id not found... adding it ");
          return [
            ...prevState,
            {
              id,
              networkId: networkId, // Update as per requirement
              agentUserId: agentUserId ?? null,
              localities: localityDetails,
            },
          ];
        }
      });
    },
    [setNewLocalities2]
  );

  const handleUpdate = async (attribute: string, value: any, domainId: number) => {
    try {
      const formData = new FormData();
      formData.append("updateType", "network_domain");
      formData.append("domainId", domainId.toString());
      formData.append("attribute", attribute);
      formData.append("value", value.toString());
      console.log(value, "11111111111111111")
      console.log(attribute, "11111111111111111")
      await fetcher.submit(formData, { method: "POST" });

      showToast(`${attribute.replace(/([A-Z])/g, " $1")} updated successfully`, "success");
      // Update UI only after successful response
    } catch (error) {
      showToast(`Failed to update ${attribute.replace(/([A-Z])/g, " $1")}`, "error");
    }
  };
  const handleMarkerClick = useCallback(
    (id: number,
      areaDetails: NetworkAreas
    ) => {
      setInfoWindowShown((prevState) => {
        if (prevState.isShown) {
          if (prevState.areaId === id) {

            return { areaId: null, isShown: false, selectedAreaDetails: null };
          } else {

            return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };
          }
        } else {

          return { areaId: id, isShown: true, selectedAreaDetails: areaDetails };
        }
      });
    },
    []
  );

  const { showToast } = useToast()


  const updateToggle = (sellerId: number) => {
    const formData = new FormData()
    formData.append("sellerId", sellerId as unknown as string)
    formData.append("netWorkId", networkId as unknown as string)
    formData.append("actionType", "updateNetWorkStatus")
    fetcher.submit(formData, { method: "POST" })
    if (fetcher.state === "idle" && fetcher.data !== undefined
    ) {
      showToast("NetWorkStatus updated Successfully", "success")
    }
  }
  const handleLocateShopClicked = useCallback((state: boolean) => { setisLocateShopClicked(state); }, []
  );

  const handleAddLocalityClicked = useCallback((state: boolean) => {
    setisAddLocalityClicked(state);
  }, []
  );

  const submit = useSubmit(); // ✅ Ensure useSubmit() is defined inside the component
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    console.log("handle Submit Called......")
    event.preventDefault();
    console.log("🚀 handleSubmit Called!"); // ✅ Debugging log
    const formData = new FormData();
    formData.append("actionType", "addLocalities"); // or "createAgent"
    const transformed = newLocalities2.map(item => ({
      networkId: item.networkId ?? 0,
      agentUserId: parseInt(item.agentUserId ?? "0"),     // Use the item's agentUserId or default to 0 if null
      areas: [item.id ?? 0]
    }));
    formData.append("localities", JSON.stringify(transformed));
    console.log("📩 Form Data Before Submit:", Object.fromEntries(formData));

    submit(formData, { method: "post", encType: "multipart/form-data" });
    handleAddLocalityClicked(false); resetNewLocalities();
  };

  const handleMasterLocalitiesClicked = useCallback(async (userId: number, state: string, district: string) => {
    if (!state || !district) {
      alert("Please select a state and district to proceed...");
      return;
    }

    try {
      const response = await fetch(`./api/masterLocalities?userId=${userId}&state=${state}&district=${district}`);
      //apps/business-console/app/routes/api/masterLocalities.ts
      const data = await response.json();


      if (!response.ok) {
        throw new Error(data.error || "Unknown error");
      }
      setMasterLocalities(data.masterLocalities.data);
    } catch (error) {
      console.error("Error fetching Master Localities:", error);
      alert("Fetching Master Localities failed. Please try again.");
    }
  }, []);

  useEffect(() => {
    if (map && networkAreas) {
      const decodedPolygons = networkAreas.map((area) => ({
        id: area.networkAreaId,
        paths: area.encodedPolygon ? decodePolygon(area.encodedPolygon) : [],
      }));
      setVisibleAreas(new Set(decodedPolygons.map((polygon) => polygon.id))); // Track which polygons to show
    }
  }, [map, networkAreas]);

  useEffect(() => {
    // Set loading to false only when both API data and map are ready
    if (googleMapsApiKey && networkAreas) {
      if (mapLoaded) {
        setIsLoading(false); // Everything is ready
      }
    }
  }, [googleMapsApiKey, networkAreas, mapLoaded]);

  const onUnmount = useCallback(() => {
    setMap(null)
    setMapLoaded(false)
  }, [])

  const getPolygonCenter = (paths: google.maps.LatLngLiteral[]) => {
    const latitudes = paths.map((path) => path.lat);
    const longitudes = paths.map((path) => path.lng);

    const latSum = latitudes.reduce((a, b) => a + b, 0);
    const lngSum = longitudes.reduce((a, b) => a + b, 0);

    return {
      lat: latSum / latitudes.length,
      lng: lngSum / longitudes.length,
    };
  };

  const NetworkHeaders = [
    "Id",
    "Name",
    "BusinessName",
    "Status"
  ];
  const NetworkBannerHeaders = [
    "Id",
    "Banner Image",
    "Sequence",
    "Action"
  ];
  const NetworkBuyerHeaders = [
    "Id",
    "Name",
    "Mobile number",
    "Agent Name",
    "OverRide Agent Name"
  ];

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalBannerOpen, setIsModalBannerOpen] = useState(false);

  const navigation = useNavigate();
  const NetWorkSeller = [
    "Seller Id",
    "Seller Name",
    "Actions",
  ]
  const fetcher = useFetcher();


  const [toggledNetWork, setToggleNetwork] = useState<Record<number, boolean>>(() =>
    networkSeller?.reduce((acc, seller) => {
      acc[seller.networkSellerId] = seller.disabled;
      return acc;
    }, {} as Record<number, boolean>)

  )

  const handleSwitch = async (networkSellerId: number) => {

    setToggleNetwork((prev) => ({
      ...prev,
      [networkSellerId]: !prev[networkSellerId]
    }))
    updateToggle(networkSellerId);


  }
  const handleSwitchBanner = (bannerId: number) => {
    const formData = new FormData()
    formData.append("bannerId", bannerId as unknown as string)
    formData.append("actionType", "updateBanner")
    formData.append("netWorkId", networkId.toString())


    fetcher.submit(formData, { method: "POST" })
  }
  const [agentStauts, setAgentStatus] = useState<{ [key: number]: boolean }>({})
  const handleToggleStatus = (agentId: number, currentStatus: boolean) => {
    const newStatus = !currentStatus;
    setAgentStatus((prev) => ({ ...prev, [agentId]: newStatus }));
    const formData = new FormData();
    formData.append("_intent", "updateAgentStatus");
    formData.append("agentUserId", agentId.toString());
    formData.append("networkId", networkId.toString());
    formData.append("status", newStatus.toString()); // Convert boolean to string
    fetcher.submit(formData, { method: "put" });
  };

  const [isnBannerUpdate, setIsBannerUpdate] = useState<{ [key: number]: boolean }>({});
  const [nBannerSeq, setNBannerSeq] = useState<{ [key: number]: string }>({})
  const [isnBannerUpdateurl, setIsBannerUpdateurl] = useState<{ [key: number]: boolean }>({});
  const [nBannerurl, setNBannerurl] = useState<{ [key: number]: string }>({})
  const [currentRowId, setCurrentRowId] = useState<number | null>(null);

  const uploadFetcher = useFetcher<{ success: boolean; fileUrl: string; error?: string }>();
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleUpdateSeq = (id: number, val: string) => {
    setNBannerSeq((prev) => ({ ...prev, [id]: val }));
  };


  const handleSaveSeq = (seqId: number, row: NetworkBanner) => {
    const formData = new FormData();
    formData.append("actionType", "bannerSequence")
    formData.append("bannerData", JSON.stringify(row))
    formData.append("seQuenceId", nBannerSeq[seqId].toString())
    fetcher.submit(formData, { method: "PUT" })
    setIsBannerUpdate((prev) => ({ ...prev, [seqId]: false }));
  }
  const handleSaveUrl = (seqId: number, row: NetworkBanner) => {
    const formData = new FormData();
    formData.append("actionType", "updateBannerUrl")
    formData.append("bannerData", JSON.stringify(row))
    formData.append("bannerUrl", nBannerurl[seqId].toString())
    fetcher.submit(formData, { method: "PUT" })
    setIsBannerUpdate((prev) => ({ ...prev, [seqId]: false }));
  }

  useEffect(() => {
    if (uploadFetcher.data && currentRowId !== null) { // Ensure currentRowId is not null
      if (uploadFetcher.data.error) {
        setUploadError(uploadFetcher.data.error);
      } else if (uploadFetcher.data.fileUrl) {
        const uploadedUrl = uploadFetcher.data.fileUrl;

        setNBannerurl((prev) => ({
          ...prev,
          [currentRowId!]: uploadedUrl, // `!` asserts it's not null
        }));

        setPreviewUrls((prev) => ({
          ...prev,
          [currentRowId!]: uploadedUrl, // `!` asserts it's not null
        }));

        setUploadError(null);
        if (fileInputRef.current) fileInputRef.current.value = "";
      }
    }
  }, [uploadFetcher.data]); // Ensure currentRowId is included in dependencies


  const [isBuyerModal, setIsBuyerModal] = useState(false);
  const [selectedBuyer, setSelectedBuyer] = useState({});
  const [searchBuyerName, setSearchBuyerName] = useState('');
  const debounceSearchTerm = useDebounce(searchBuyerName, 500);
  useEffect(() => {
    fetchfor(`?networkId=${networkId}&networkName=${networkName}&activeTab=${activeTab}&pageSize=${pageSize}&currentPage=${currentPage}&matchBy=${debounceSearchTerm}`);
  }, [debounceSearchTerm])

  const handleSave = (updatedData: any) => {
    console.log("Updated Data:", updatedData);
    const formData = new FormData()
    formData.append("intent", "updateOverrideAgentName");
    formData.append("networkBuyerId", updatedData.networkBuyerId.toString());
    formData.append("overrideAgentId", updatedData.overrideAgentId.toString());
    fetcher.submit(formData, { method: "put" })
    setIsBuyerModal(false);
    setSelectedBuyer({});
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>, rowId: number) => {
    setCurrentRowId(rowId); // Track the row being updated
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setUploadError(null);

    const MAX_FILE_SIZE = 500 * 1024; // 5MB limit
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];

    const validFile = files.find((file) => {
      if (file.size > MAX_FILE_SIZE) {
        setUploadError("File size exceeds 500kb limit.");
        return false;
      }
      if (!allowedTypes.includes(file.type)) {
        setUploadError("Only JPEG, PNG, GIF, and WEBP images are allowed.");
        return false;
      }
      return true;
    });

    if (!validFile) return;

    const uploadFormData = new FormData();
    uploadFormData.append("_action", "uploadImage");
    uploadFormData.append("file", validFile, validFile.name);

    uploadFetcher.submit(uploadFormData, {
      method: "post",
      action: "/home/<USER>",
      encType: "multipart/form-data",
    });
  };

  const loading = fetcher.state !== "idle";


  return (

    <div className="h-full">
      <h1 className=" mb-4 font-bold cursor-pointer" onClick={() => navigation("/home/<USER>")}> <span className="text-2xl">Network Management / </span> <span className="text-xl">{networkName} </span> </h1>
      <Tabs value={activeTab} onValueChange={handleTabChange}>
        {loading && <SpinnerLoader loading={loading} />}
        <TabsList>
          {!salesPermissions && <TabsTrigger value="NetworkConfig">Configurations</TabsTrigger>
          }
          {!salesPermissions && <TabsTrigger value="NetworkSeller">Sellers</TabsTrigger>
          }
          <TabsTrigger value="NetworkAreas">Areas</TabsTrigger>
          <TabsTrigger value="NetworkAgents">Agents</TabsTrigger>
          <TabsTrigger value="NetworkBanners">NetworkBanners</TabsTrigger>
          <TabsTrigger value="NetworkBuyers">NetworkBuyers</TabsTrigger>

        </TabsList>
        <TabsContent value='NetworkSeller'>
          <ResponsiveTable
            headers={NetWorkSeller}
            data={
              networkSeller.filter(a => !a.disabled)
            }
            renderRow={(row) => {
              return (
                <tr key={row.sellerId} className="border-b">
                  <td className="py-2 px-3 text-center  whitespace-normal break-words">{row.sellerId}</td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words">
                    {row?.seller || "-"}
                  </td>
                  <td className="py-2 px-3 text-center  whitespace-normal break-words">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-500 hover:text-red-900"
                      onClick={() => handleSwitch(row?.networkSellerId)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </td>

                </tr>
              )
            }
            }
          />
          <Button className="fixed bottom-5 right-5 rounded-full" onClick={() => setIsModalOpen(true)}>
            + Add Seller
          </Button>
          <NetWorkModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)
          } sellerList={sellerList ? sellerList.filter(a => !networkSeller.filter(a => !a.disabled).some(ns => ns.sellerId === a.id)) : []} netWorkId={networkId}
          />
        </TabsContent>
        <TabsContent value='NetworkBanners'>
          <ResponsiveTable
            headers={NetworkBannerHeaders}
            data={
              networkBanners || []
            }
            renderRow={(row) => (
              <tr key={row.id} className="border-b">
                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.id || "-"}
                </td>
                <td className="py-2 px-4 text-center whitespace-normal break-words">
                  <div className="flex flex-col items-center gap-3">

                    {isnBannerUpdateurl[row.id] ? (
                      <div className="flex flex-col items-center gap-3 p-4 bg-gray-100 rounded-lg shadow-md ">

                        {/* File Upload Input */}
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileSelect(e, row.id)}
                          className="file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm 
                     file:font-semibold file:bg-blue-100 file:text-blue-700 
                     hover:file:bg-blue-200 transition duration-200"
                        />

                        {/* Image Preview Section */}
                        <div className='flex flex-row gap-2'>
                          {previewUrls[row.id] && (
                            <div className="flex flex-col items-center p-2 border border-gray-300 rounded-lg bg-white shadow-sm">
                              <span className="text-md font-bold text-red-600">Upload Image 1000*400 Size</span>
                              <span className="text-sm text-gray-600">Image Preview</span>
                              <img
                                src={previewUrls[row.id]}
                                alt="Preview"
                                className="mt-2 rounded-md w-[250px] h-[100px] object-cover"
                              />
                            </div>
                          )}

                          {/* Action Buttons */}
                          <div className="flex  flex-row items-center gap-3">
                            <Save
                              size={24}
                              className="cursor-pointer text-green-600 hover:text-green-700 transition duration-200"
                              onClick={() => handleSaveUrl(row.id, row)}
                            />
                            <X
                              size={24}
                              className="cursor-pointer text-red-500 hover:text-red-600 transition duration-200"
                              onClick={() => {
                                setIsBannerUpdateurl((prev) => ({ ...prev, [row.id]: false })); // Close input
                                setPreviewUrls((prev) => ({ ...prev, [row.id]: row.bannerUrl })); // Reset preview
                                setNBannerurl((prev) => ({ ...prev, [row.id]: row.bannerUrl })); // Reset stored image
                                if (fileInputRef.current) fileInputRef.current.value = ""; // Clear file input
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-row items-center gap-2">
                        {/* Display Image */}
                        <img
                          alt="img"
                          src={nBannerurl[row.id] || row.bannerUrl}
                          className="rounded-lg shadow-md w-[250px] h-[100px] object-cover"
                        />

                        {/* Edit Button */}
                        <Pencil
                          className="cursor-pointer text-blue-600 hover:text-blue-700 transition duration-200"
                          onClick={() => setIsBannerUpdateurl({ [row.id]: true })}
                        />
                      </div>
                    )}

                  </div>
                </td>



                <td className="py-2 px-3 text-center  whitespace-normal break-words max-w-20">
                  <div className='flex flex-row items-center justify-center gap-2'>

                    {isnBannerUpdate[row.id] ?
                      <>
                        <Input
                          type="number"
                          value={nBannerSeq[row.id] ?? row.sequenceId}
                          onChange={(e) => handleUpdateSeq(row.id, e.target.value)}
                          className=" px-2 py-1 border border-gray-300 rounded-md"

                        />
                        <Save size={24} onClick={() => handleSaveSeq(row.id, row)} className='cursor-pointer' />
                        <X
                          color="red"
                          size={24}
                          className="cursor-pointer text-red-500"
                          onClick={() => setIsBannerUpdate({})} // Close all inputs
                        />
                      </> :
                      <>
                        {row?.sequenceId || "-"}
                        <Pencil size={20} className='cursor-pointer self-center' onClick={() => setIsBannerUpdate({ [row.id]: true })} />
                        {row.active == false && <span className='bg-orange-100 p-1 rounded-full px-2  text-red-600 font-bold'>disabled</span>}

                      </>
                    }


                  </div>
                </td>
                <td className="py-2 px-3 text-center  whitespace-normal break-words">

                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-900"
                    onClick={() => {
                      if (confirm("Are you sure you want to delete this Network Banner?")) {
                        handleSwitchBanner(row?.id);
                      }
                    }}
                    style={{ alignSelf: "flex-end" }}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            )}
          />
          {!salesPermissions && <Button className="fixed bottom-5 right-5 rounded-full" onClick={() => setIsModalBannerOpen(true)}
          >
            + Add Banner
          </Button>}

          <CreateNetworkBannerModal
            isOpen={isModalBannerOpen}
            onClose={() => setIsModalBannerOpen(false)}
            networkId={networkId}

          />
          {/* <CreateAgent isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} NetWorkId={networkId}
            sellerList={sellerList}
            roles={roles}
          /> */}
        </TabsContent>
        <TabsContent value='NetworkAgents'>
          <ResponsiveTable
            headers={NetworkHeaders}
            data={
              networkAgents || []
            }
            renderRow={(row) => (
              <tr key={row.agentUserId} className="border-b">
                <td className="py-2 px-3 text-center  whitespace-normal break-words">{row.agentUserId}</td>


                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.fullName || "-"}
                </td>
                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.businessName || "-"}
                </td>
                <td className="py-2 px-3 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <Switch
                      checked={!(agentStauts[row.agentUserId] || row.status)}
                      onCheckedChange={() => handleToggleStatus(row.agentUserId, agentStauts[row.agentUserId])}
                    />


                  </div>
                </td>
              </tr>
            )}
          />
          {!salesPermissions && <Button className="fixed bottom-5 right-5 rounded-full" onClick={() => setIsModalOpen(true)}>
            + Add Agent
          </Button>}
          <CreateAgent isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} NetWorkId={networkId}
            sellerList={sellerList}
            roles={roles}
          />
        </TabsContent>
        <TabsContent value='NetworkBuyers'>
          <div className="flex justify-between mb-4">
            <Input
              placeholder="Search by Name, Mobile"
              value={searchBuyerName}
              type='search'
              onChange={(e) => setSearchBuyerName(e.target.value)}
              className="max-w-sm  rounded-full"
            />

            <Select value={String(pageSize)} onValueChange={(newPageSize) => fetchfor(`?networkId=${networkId}&networkName=${networkName}&activeTab=${activeTab}&pageSize=${newPageSize}&currentPage=${currentPage}`)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Items per page" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 per page</SelectItem>
                <SelectItem value="10">10 per page</SelectItem>
                <SelectItem value="20">20 per page</SelectItem>
                <SelectItem value="50">50 per page</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <ResponsiveTable
            headers={NetworkBuyerHeaders}
            data={
              networkBuyers || []
            }
            renderRow={(row) => (
              <tr key={row.networkBuyerId} className="border-b">
                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.networkBuyerId || "-"}
                </td>
                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.buyerName || "-"}
                </td>
                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.mobileNumber || "-"}
                </td>
                <td className="py-2 px-3 text-center  whitespace-normal break-words">
                  {row?.agentName || "-"}
                </td>
                <td className="py-2 px-3 text-center  whitespace-normal break-words flex flex-row gap-3 items-center justify-end">
                  <div>
                    {row?.overrideAgentName || "-"}
                  </div>
                  <div>
                    <Edit size={20} className='cursor-pointer' onClick={() => {
                      setIsBuyerModal(true);
                      setSelectedBuyer(row)
                    }} />
                  </div>
                </td>
              </tr>
            )}
          />
          <div className="flex items-center justify-center space-x-2 py-4 overflow-x-auto whitespace-nowrap">
            <h2 className="shrink-0">Current Page: {currentPage + 1}</h2>
            <div className="overflow-x-auto">
              <ResponsivePagination
                totalPages={Number(pageSize)}
                currentPage={currentPage}
                onPageChange={(newPageNum) => fetchfor(`?networkId=${networkId}&networkName=${networkName}&activeTab=${activeTab}&pageSize=${pageSize}&currentPage=${newPageNum}`)}
              />
            </div>
          </div>
          <EditBuyerModal
            isOpen={isBuyerModal}
            networkBuyer={selectedBuyer || {}}
            networkAgents={networkAgents || []}
            onClose={() => {
              setIsBuyerModal(false)
              setSelectedBuyer({});
            }}
            onSave={handleSave}
          />
        </TabsContent>
      </Tabs>
      {activeTab === "NetworkConfig" && (networkConfig
        ? (<NetWorkConfig networkConfig={networkConfig} networkName={networkName} onAttributeUpdate={handleUpdate} />
        ) : (<div className="flex items-center justify-center h-full">
          <p className="text-red-500">Unable to find network configurations.</p>
        </div>))}
      {activeTab === "NetworkAreas" && (
        <LoadScript googleMapsApiKey={googleMapsApiKey}>
          <div className="flex w-full h-[87vh] border rounded-xl border-neutral-200 my-4">
            <div className="flex flex-col gap-4 w-[20vw] overflow-auto bg-white shadow rounded-xl p-3">
              {dataleng && networkAreas ? (
                networkAreas?.map((network, index) => (
                  <NetworkAreaCard
                    networkAreaDetail={network}
                    agentDetails={networkAgents}
                    networkId={networkId}
                    color={getPolygonColor(index)} netWorks={networkAreas}
                    isSalesPermission={salesPermissions}

                  />
                ))
              ) : (
                <div className="h-24 text-center">
                  No Network Areas found.
                </div>
              )}
            </div>

            <div className="relative flex-1 h-full bg-white">
              <div className='absolute flex z-10 top-0 left-0 w-full justify-between p-3 max-h-full'>
                {!salesPermissions && !isAddLocalityClicked ? (<div className='p-3 h-fit bg-white rounded-xl flex gap-2 text-blue-600 items-center shadow'>
                  <button
                    onClick={() => { handleAddLocalityClicked(true); resetNewLocalities() }}
                  >
                    + &nbsp; Add New Locality
                  </button>
                </div>) : (!salesPermissions && <div className='p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow '>

                  <div className='flex w-full justify-between'>
                    Add Localities
                    <button onClick={() => { handleAddLocalityClicked(false); resetNewLocalities(); }}><X size={16} /></button>
                  </div>
                  <div className='flex w-full p-1 items-center gap-3'>

                    <Select value={selectedState} onValueChange={setSelectedState}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        {stateList.map((state) => (
                          <SelectItem key={state} value={state}>{state}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Select value={selectedDistrict} onValueChange={(newDistrict) => {
                      setSelectedDistrict(newDistrict); // Update selected district
                      handleMasterLocalitiesClicked(userId, selectedState, newDistrict); // Fetch data
                      resetNewLocalities(); // Reset localities
                    }} disabled={!selectedState}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select District" />
                      </SelectTrigger>
                      <SelectContent>
                        {statesAndDistricts
                          ?.filter((abc) => abc.state === selectedState)
                          .map((district) => (
                            <SelectItem key={district.district} value={district.district}>
                              {district.district}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>


                  </div>
                  <div className='flex flex-col gap-2 self-start max-h-full w-full overflow-auto p-2 '>
                    {masterLocalities && masterLocalities.length > 0 ? (
                      (masterLocalities.filter(a => networkAreas?.some(na => na.areaId != a.id)).length > 0 ? masterLocalities.filter(a => !networkAreas?.some(na => na.areaId === a.id)) : masterLocalities).map((locality, index) => (
                        <>
                          <div key={locality.id}>
                            <label className="cursor-pointer flex items-center gap-2 p-1" htmlFor={`locality-${locality.id}`}>
                              <Checkbox
                                id={`locality-${locality.id}`}
                                checked={newLocalities.some((abc) => abc.id === locality.id)}
                                onClick={() => handleNewLocalities(locality.id, locality)}
                              />
                              <div className='flex flex-col gap-2'>
                                <span>{locality.id} - {locality.name}</span>
                                {newLocalities.some((abc) => abc.id === locality.id) && (
                                  <Select

                                    value={
                                      newLocalities2.find((loc) => loc.id === locality.id)?.agentUserId || ""
                                    }

                                    onValueChange={(newDistrict) => {
                                      handleNewLocalities2(locality.id, locality, networkId, newDistrict);

                                    }} disabled={!selectedState}>
                                    <SelectTrigger className="w-[280px]">
                                      <SelectValue placeholder="Select sales agent" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {networkAgents?.map((district) => (
                                        <SelectItem key={district.agentUserId} value={district.agentUserId.toString()}>
                                          {district.agentUserId} - {district.fullName}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                )}
                              </div>
                            </label>
                          </div>
                          {index < masterLocalities.length - 1 && (<div className='border-b border-neutral-200'></div>)} </>
                      ))
                    ) : (
                      <div>No Localities Fetched yet</div>
                    )} </div>
                  <form method="post" encType="multipart/form-data" onSubmit={handleSubmit}>

                    <input type="hidden" name="localities" value={JSON.stringify(newLocalities2)} />

                    <button
                      type="submit"
                      disabled={newLocalities2.length === 0}
                      className="px-4 py-2 bg-primary text-white rounded disabled:bg-neutral-500"
                    >
                      Submit Localities
                    </button>
                  </form>
                </div>)}
                {!isLocateShopClicked ? (<div className='p-3 bg-white  h-fit rounded-xl flex gap-2 text-blue-600 items-center shadow'>
                  <button
                    onClick={() => handleLocateShopClicked(true)}
                  >
                    📍 &nbsp; Locate a shop
                  </button>
                </div>) :
                  (<div className='p-3 bg-white rounded-xl flex flex-col gap-2  items-center shadow'>

                    <div className='flex w-full justify-between'>
                      Locate a shop
                      <button onClick={() => handleLocateShopClicked(false)}><X size={16} /></button>
                    </div>
                    <div className='flex w-full p-1 items-center justify-between'>
                      <p>Latitude : </p>
                      <input type='text' className='border border-neutral-400 rounded-md p-1'

                        onChange={(e) => setLatitude(parseFloat(e.target.value))} />
                    </div>
                    <div className='flex gap-1 p-1 items-center'>
                      <p>Longitute : </p>
                      <input type='text' className='border border-neutral-400 rounded-md p-1'

                        onChange={(e) => setLongitude(parseFloat(e.target.value))}
                      />
                    </div>
                    <button className='text-primary border border-primary p-2 rounded-md'
                      onClick={() => handleFindLocation(latitude, longitude, true)}
                    >
                      📍 &nbsp; Find Location
                    </button>
                  </div>)
                }
              </div>
              {!mapLoaded && (
                <div className="flex items-center justify-center h-full">
                  <div className="loader">Loading Map...</div>
                </div>
              )}
              {googleMapsApiKey ? (
                <GoogleMap
                  mapContainerStyle={MAP_CONTAINER_STYLE}
                  center={BANGALORE_CENTER}
                  zoom={11}
                  onLoad={(mapInstance) => {
                    onLoad(mapInstance);
                    setMapLoaded(true); // Set map as loaded when onLoad is called
                  }}
                  onUnmount={() => {
                    onUnmount();
                    setMapLoaded(false); // Reset when the map is unmounted
                  }}
                  options={{
                    styles: [{
                      featureType: "all",
                      elementType: "geometry.fill",
                      stylers: [{ visibility: "on" }]
                    }],
                    mapTypeControl: false,
                    streetViewControl: false,
                    fullscreenControl: false,
                    clickableIcons: false,        // Disable default map icons (e.g., POIs)
                    gestureHandling: "auto",      // Allow zoom and scroll interactions
                  }}
                >
                  {mapLoaded ? (networkAreas?.filter((abc) => visibleAreas.has(abc.networkAreaId))?.map((area, index) => (
                    area.encodedPolygon
                    && (

                      <Polygon
                        key={area.networkAreaId}
                        paths={decodePolygon(area.encodedPolygon)}
                        options={{
                          fillColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor(index),
                          fillOpacity: 0.2,
                          strokeColor: isAddLocalityClicked ? "#3b82f6" : getPolygonColor(index),
                          strokeOpacity: 1,
                          strokeWeight: 2,
                          draggable: false,
                          editable: false,
                          geodesic: false,
                          zIndex: 1,
                          clickable: true, // Ensure polygons remain clickable
                        }}
                        onClick={() => handleMarkerClick(area.networkAreaId, area)}

                      />
                    )
                  ))) : null}
                  {mapLoaded ? (newLocalities?.map((area, index) => (
                    area.polygon
                    && (

                      <Polygon
                        key={area.id}
                        paths={decodePolygon(area.polygon)}
                        options={{
                          fillColor: "#10b981",
                          fillOpacity: 0.4,
                          strokeColor: "#10b981",
                          strokeOpacity: 1,
                          strokeWeight: 2,
                          draggable: false,
                          editable: false,
                          geodesic: false,
                          zIndex: 10,
                          clickable: false, // Ensure polygons remain clickable
                        }}
                      />
                    )
                  ))) : null}

                  {infoWindowShown && infoWindowShown.areaId && infoWindowShown.isShown && infoWindowShown.selectedAreaDetails && (

                    <>
                      {console.log("entered infowindow params with ##########", infoWindowShown)}
                      <InfoWindow
                        position={getPolygonCenter(decodePolygon(infoWindowShown.selectedAreaDetails.encodedPolygon))}
                        onCloseClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}
                        options={{
                          headerDisabled: true,
                          minWidth: 200,
                          disableAutoPan: true,
                        }}
                      >
                        <div className="flex flex-col gap-2 overflow-hidden ">
                          <div className='flex justify-between w-full align-middle items-center'>
                            <h2 className='text-md font-semibold text-typography-300'>Locality Info</h2>
                            <button className="inline-flex items-center gap-1 hover:text-blue-800"
                              onClick={() => setInfoWindowShown({ isShown: false, areaId: null, selectedAreaDetails: null })}>
                              <X className="h-5 w-5" />
                            </button>
                          </div>
                          <div className="flex flex-col gap-1 text-sm font-medium text-typography-700 w-[calc(100%-1rem)]">
                            <p><span className='text-sm text-typography-300 font-thin'>Area id:</span> {infoWindowShown.selectedAreaDetails?.networkAreaId}</p>
                            <p><span className='text-sm text-typography-300 font-thin'>Area Name: </span> {infoWindowShown.selectedAreaDetails?.networkAreaName}</p>
                            <p><span className='text-sm text-typography-300 font-thin'>Sales Agent:</span> {infoWindowShown.selectedAreaDetails?.agentName}</p>
                          </div>
                        </div>
                      </InfoWindow>
                      <style>
                        {`.info-window-wrapper {
                                  padding: 10px; 
                                }
                              
                                .gm-style-iw { 
                                  padding: 12px !important; 
                                }
                                .gm-style-iw-d { 
                                  padding: 0px !important; 
                                  overflow:hidden !important;
                                }`}
                      </style> </>
                  )}
                  {showMarker && pointerLocation && pointerLocation.latitude !== null &&
                    pointerLocation.longitude !== null && (
                      <><Marker
                        position={{
                          "lat": pointerLocation.latitude,
                          "lng": pointerLocation.longitude
                        }}
                      /></>)}
                </GoogleMap>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-red-500">Google Maps API key is missing.</p>
                </div>
              )}
            </div>
          </div>
        </LoadScript>)}
    </div>
  );
}

