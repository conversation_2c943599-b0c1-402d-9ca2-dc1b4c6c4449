import {
  j<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLocation,
  useRouteLoaderData,
} from "@remix-run/react";
import type { LinksFunction } from "@remix-run/node";
import styles from "./tailwind.css?url"
import GlobalSpinnerLoader from "./components/loader/GlobalSpinnerLoader";
import ErrorBoundary from "./components/error/ErrorBoundary";
import { ToastProvider } from "./components/ui/ToastProvider";
import { Toaster } from "./components/ui/toaster";
import { handleBuyerAppAutoLogin } from "./utils/auth.server";
import { getNetworkTheme } from "./services/netWorks";
import { NetworkTheme } from "./types/api/common";
import { useNavigate } from "@remix-run/react";
import { useEffect } from "react";
import { themeCache, extractUserIdFromToken, generateCacheKey } from "./utils/cache";
import { getSession } from "./utils/session.server";

export const links: LinksFunction = () => [
  { rel: "stylesheet", href: styles },
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
  { 
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap",
  },
  {
    rel: "icon",
    type: "image/png",
    sizes: "192x192",
    href: "https://ik.imagekit.io/u7uktwxu0/Business%20Icons/fevicon-business%20console.svg",
  },
  {
    rel: "icon",
    type: "image/png",
    sizes: "512x512",
    href: "https://ik.imagekit.io/u7uktwxu0/Business%20Icons/fevicon-business%20console.svg",
  },
];

interface LoaderData {
  theme:  "light";
  sellerDomainConfig: NetworkTheme;
}

export const loader = async ({ request }: { request: Request }) => {
  const theme = request?.headers?.get("sec-ch-prefers-color-scheme") || "light";
  const buyerAppLoginResult = await handleBuyerAppAutoLogin(request);
  if (buyerAppLoginResult) {
    return buyerAppLoginResult;
  }
  
  // Extract userId from session for secure cache key
  const session = await getSession(request.headers.get("Cookie"));
  const token = session.get("access_token");
  const userId = extractUserIdFromToken(token);
  
  // Generate secure cache key using domain and userId
  const url = new URL(request.url);
  const cacheKey = generateCacheKey(url.hostname, userId);
  
  try {
    const sellerDomainConfig = await themeCache.getOrSet(
      cacheKey,
      async () => {
        console.log("Fetching fresh theme data from API");
        const response = await getNetworkTheme(request);
        if (!response.data) {
          throw new Error("No theme data received from API");
        }
        return response.data;
      },
      15 * 60 * 1000 // 15 minutes TTL
    );

    console.log(sellerDomainConfig, "sellerDomainConfig");
    
    return json({ 
      theme, 
      sellerDomainConfig 
    });
  } catch (error) {
    console.log("Error fetching theme data:", error);
    
    // Return default theme data
    return json({ 
      theme,
      sellerDomainConfig: {} as NetworkTheme
    });
  }
};

export function Layout({ children }: { children: React.ReactNode }) {
  const navigate = useNavigate();
  const loader = useRouteLoaderData<LoaderData>("root");  
  const location = useLocation();

  const theme = loader?.theme || "light";
  const sellerDomainConfig = loader?.sellerDomainConfig;

  useEffect(() => {
    if(sellerDomainConfig?.networkType === "B2C" 
      && sellerDomainConfig?.ondcDomain === "RET11" 
      && (location.pathname === "/home" 
        || location.pathname === "/" 
        || location.pathname === "/home/<USER>" 
        || location.pathname === "/home/<USER>")){
      navigate("/sellerSetting");
      return;
    }
  }, [sellerDomainConfig?.defaultSellerId]);
  
  return (
    <html lang="en" className={theme}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body className={theme === "light" ? "bg-white text-black" : "bg-white text-black"}>
        <GlobalSpinnerLoader />
        <ToastProvider>
          {children}
        </ToastProvider>
        <Toaster />
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <>
    <Outlet />

  </>
}


export { ErrorBoundary };

