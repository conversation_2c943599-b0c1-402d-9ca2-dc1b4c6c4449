import { Ticket } from "./Tickets";

export type OrderStatus =
  | "Created" // Order placed but not accepted
  | "Accepted" // Order accepted by seller
  | "Packed" // Order has been packed
  | "Assigned" // Order assigned to delivery agent
  | "PickedUp" // Order picked up by delivery agent
  | "Dispatched" // Order dispatched for delivery
  | "Delivered" // Order successfully delivered
  | "Cancelled" // Order has been cancelled

export type LogisticStatus =
  | "LOG_CREATED"
  | "LOG_PENDING"
  | "LOG_SEARCHING_AGENT"
  | "LOG_AGENT_ASSIGNED"
  | "LOG_AT_PICKUP"
  | "LOG_PICKED_UP"
  | "LOG_REACHED_LOCATION"
  | "LOG_DELIVERED"
  | "LOG_RTO_INITIATED"
  | "LOG_CANCELLED";


export interface rNETOrder {
  // Basic order info
  orderGroupId: number
  orderStatus: OrderStatus
  logStatus: LogisticStatus
  createdAt: string
  updatedAt: string

  // Business Info
  sellerId: number
  sellerName: string
  sellerMobile: string
  buyerId: number
  buyerName: string
  buyerMobile: string
  nBuyerId: number
  networkId: number
  networkName: string
  agentUserId: number
  agentName: string
  sellerInventoryId: number

  // Delivery info
  deliveryDate: string | null
  deliveryType: "DELIVERY" | "TAKEAWAY"
  deliveryOtp: string
  pickupOtp: string
  delAssignedTo: number
  deliveredById: number
  deliveredTime: string | null

  // Timeline eg. "2024-01-15T11:30:00Z" 
  acceptedTime: string | null
  packedTime: string | null
  assignedTime: string | null
  pickedUpTime: string | null
  deliveryStartTime: string | null

  // Financial Info
  itemsTotalAmount: number
  totalOrderGroupAmount: number
  totalDeliveryCharge: number
  codAmount: number
  walletAmount: number
  totalCreditAmount: number
  creditPendingAmount: number
  creditCollectedOfflineAmount: number
  isCreditUsed: boolean
  creditCollectedOffline: boolean
  creditCollectedTime: string | null
  paymentCompleted: boolean

  // Tax & GST Info
  gstTotalAmount: number
  igstApplicable: boolean
  totalTaxAmount: number
  itemsTaxAmount: number
  packagingTaxAmount: number
  platformTaxAmount: number
  deliveryTaxAmount: number

  // Platform Charges
  packagingCharges: number
  platformFee: number
  pcBasic: number
  pcAgent: number
  pcTotalWithGst: number
  pcApplied: boolean
  agentComm: number
  salesComm: number

  // Item & Quantity Info
  orderDetails: TripOrderDetailEventData[]
  totalItems: number
  totalQty: number
  weight: number
  totalBoxes: number
  totalBags: number
  pickedItemsCount: number
  pickedItemsWeight: number
  deliveredItems: number
  cancelledItems: number

  // Cancelled & Returned Info
  cancelledWeight: number
  cancelledAmount: number
  dispatchedWeight: number
  dispatchedAmount: number
  returnedWeight: number
  returnedAmount: number

  // Discount & Coupon Info
  totalDiscountAmount: number
  totalItemsStrikeoffAmount: number
  discountId: number
  couponId: number
  couponCode: string
  couponDiscountAmount: number

  // Address Info
  bAreaId: number
  bAreaName: string
  bAddress: string
  bAddressName: string
  geoLocationLat: number
  geoLocationLng: number

  // Trip Info
  tripId: number
  tripSeqNumber: number

  // Additional Info
  ondcDomain: "RET10" | "RET11"
  fulfillmentType: "DELIVERY" | "TAKEAWAY"
  sellerMessage: string
  preconfirmUid: string
  supplierItemAmount: number
  supplierItemWeight: number

  //pos
  pos: "none" | "petpooja"

  // Logistics Info
  logisticProvider: "SELF" | "MP2"
  logisticDetails?: {
    // Basic Info
    id: number
    createdAt: string | null
    updatedAt: string | null
    orderGroupId: number

    // MP2 Order Info
    mp2OrderId: string
    clientOrderId: string
    orderState: string

    // LSP Info
    lspId: string
    lspName: string
    lspItemId: string
    lspNetworkOrderId: string

    // Rider Info (MP2)
    riderName: string
    riderPhone: string
    riderLastLat: number
    riderLastLng: number

    // Pricing & Distance
    price: number
    distance: number
    estDeliveryCharges: number
    estDeliveryTime: number

    // Timestamps eg. "2024-01-15T11:30:00Z" 
    mp2CreatedAt: string | null
    assignedAt: string | null
    pickedupAt: string | null
    deliveredAt: string | null
    cancelledAt: string | null
    rtoInitiatedAt: string | null
    rtoDeliveredAt: string | null
    estimatedPickupTime: string | null

    // Proof & Tracking
    pickupProof: string
    deliveryProof: string
    trackingUrl: string

    // Cancellation Info
    cancellationReasonId: number
    cancellationReasonDesc: string
    cancelledBy: number

    // Notes & Additional Info
    notes: string
    note2: string
    lNote: string
    estimateId: string

    // Logistics Handling Provider Info
    lHandlingProvider: string
    lHandlingProviderUpdatedAt: string | null
    lRiderName: string
    lRiderPhone: number
    lPrice: number
    lPotp: number
    lDotp: number
  }

  //support tickets
  supportTickets: Ticket[]
}

export interface TripOrderDetailEventData {
  orderId: number;
  itemId: number;
  itemName: string;
  packaging: string;
  itemUrl: string;
  itemRegionalLanguageName: string;
  unit: string;
  qty: number;
  price: number;
  pricePerUnit: number;
  amount: number;
  status: string;
  isReturnAllowed: boolean;
  cancelledQty: number;
  boxes: number;
  itemPicked: boolean;
  addOns?: Array<AogList>;
  posItemId: string;
  diet: string;
  variationSId?: string;
  variationName?: string;
  strikeOffAmount: number;
  itemTaxAmount?: number;
  variationId?: string;
}

export interface AogList {
  id: number;
  minSelect: number;
  maxSelect: number;
  name: string;
  description: string;
  seq: number;
  addOnItemList: AddonItem[];
}

export type Dietary = "veg" | "nonveg" | "egg" | null | undefined;

export interface AddonItem {
  id: string;
  sId: string;
  name: string;
  price: number;
  seq: number;
  qty: number;
  diet: Dietary;
}